# This Makefile can be used with GNU Make or BSD Make

LIBRARY = libmceliece6688128_clean.lib

OBJECTS = aes256ctr.obj benes.obj bm.obj controlbits.obj crypto_int16.obj \
		crypto_int32.obj crypto_uint16.obj crypto_uint32.obj crypto_uint64.obj \
		decrypt.obj encrypt.obj gf.obj operations.obj pk_gen.obj root.obj sk_gen.obj \
		synd.obj transpose.obj util.obj

# Warning C4146 is raised when a unary minus operator is applied to an
# unsigned type; this has nonetheless been standard and portable for as
# long as there has been a C standard, and we do that a lot, especially
# for constant-time computations. Thus, we disable that spurious warning.
CFLAGS=/nologo /O2 /I ..\..\..\common /W4 /wd4146 /WX

all: $(LIBRARY)

# Make sure objects are recompiled if headers change.
$(OBJECTS): *.h

$(LIBRARY): $(OBJECTS)
	LIB.EXE /NOLOGO /WX /OUT:$@ $**

clean:
	-DEL $(OBJECTS)
	-DEL $(LIBRARY)
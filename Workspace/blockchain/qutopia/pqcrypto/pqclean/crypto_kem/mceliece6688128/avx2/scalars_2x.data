{
    vec128_set2x(0X3C3CF30C0000C003, 0X0C0F0FCF0F0CF330),
    vec128_set2x(0X0CCCC3F333C0000C, 0XF0000FC33C3CCF3C),
    vec128_set2x(0X03C33F33FCC0C03C, 0X3C0F3F00C3C300FC),
    vec128_set2x(0X0003000F3C03C0C0, 0X3C33CCC0F0F3CC30),
    vec128_set2x(0XF33FF33030CF03F0, 0XC0CFFFFFCCCC30CC),
    vec128_set2x(0X0CF0303300F0CCC0, 0X3FC3F3CCFFFC033F),
    vec128_set2x(0XFF3F0C0CC0FF3CC0, 0XFC3030CCCCC0CFCF),
    vec128_set2x(0XCF3CF0FF003FC000, 0X0FCF0C00CCF333C3),
    vec128_set2x(0XC00FF3CF0303F300, 0XCFFCF33000CFF030),
    vec128_set2x(0X3CCC0CC00CF0CC00, 0X00CFFCC330F30FCC),
    vec128_set2x(0XF30FFC3C3FCCFC00, 0X3CCC3FCCC0F3FFF3),
    vec128_set2x(0X3F0FC3F0CCF0C000, 0XF00F0C3FC003C0FF),
    vec128_set2x(0X3000FF33CCF0F000, 0X330CCFCC03C0FC33)
}, {
    vec128_set2x(0X0F0F0FF0F000000F, 0XF0FFFFFFF0F00F00),
    vec128_set2x(0X00FFFFFFFF0000F0, 0X00FFF0FFFF0000FF),
    vec128_set2x(0XFFFF00FF00000F00, 0X00FF00000F0F0FFF),
    vec128_set2x(0XFFF000F00F0FF000, 0XF000F0000F00FF0F),
    vec128_set2x(0XFFF0000F0FF000F0, 0XFF000000FFF00000),
    vec128_set2x(0X00FF000FFF000000, 0XF0FF000FF00F0FF0),
    vec128_set2x(0XFF0F0FFF0F0FF000, 0X0F0F0F00FF000F0F),
    vec128_set2x(0X0FFF0000000F0000, 0X0F0F00F0F0F0F000),
    vec128_set2x(0X00F000F0FFF00F00, 0X00F00F00F00F000F),
    vec128_set2x(0X00F00FF00F00F000, 0X00F0F0F00000FFF0),
    vec128_set2x(0XFFF000F000F00000, 0XFFFFFF0FF00F0FFF),
    vec128_set2x(0X00F00F000FF00000, 0X0F0FFFF00FFFFFFF),
    vec128_set2x(0X0000FF0F0000F000, 0XFFFF0F0FFF0FFF00)
}, {
    vec128_set2x(0X00FF0000000000FF, 0X00FF00FF00FF0000),
    vec128_set2x(0XFFFFFFFFFF00FF00, 0XFF00FFFF000000FF),
    vec128_set2x(0XFF0000FF00FF0000, 0X0000FFFF000000FF),
    vec128_set2x(0XFFFF000000FF0000, 0X00FFFF00FF000000),
    vec128_set2x(0XFF00000000FF0000, 0XFFFFFF0000FF00FF),
    vec128_set2x(0X00FFFFFFFF000000, 0X0000FFFF00FFFF00),
    vec128_set2x(0XFF0000FFFFFF0000, 0XFF00FF0000FFFF00),
    vec128_set2x(0XFF00FF00FFFF0000, 0X00000000FFFFFFFF),
    vec128_set2x(0X00FFFFFFFF00FF00, 0X0000FF0000000000),
    vec128_set2x(0XFFFF000000000000, 0XFF00FFFF00FFFF00),
    vec128_set2x(0X00FF0000FF000000, 0X00FFFF00000000FF),
    vec128_set2x(0XFF00FF00FF000000, 0X0000FF00FF00FFFF),
    vec128_set2x(0X00FF00FFFF000000, 0XFF0000FFFFFF0000)
}, {
    vec128_set2x(0X000000000000FFFF, 0X0000FFFF00000000),
    vec128_set2x(0XFFFFFFFFFFFF0000, 0XFFFFFFFF0000FFFF),
    vec128_set2x(0X0000000000000000, 0X00000000FFFFFFFF),
    vec128_set2x(0XFFFF0000FFFF0000, 0X0000000000000000),
    vec128_set2x(0XFFFFFFFFFFFF0000, 0X0000FFFF00000000),
    vec128_set2x(0X0000FFFF00000000, 0XFFFF0000FFFF0000),
    vec128_set2x(0X0000FFFFFFFF0000, 0X0000FFFFFFFF0000),
    vec128_set2x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec128_set2x(0X0000FFFF00000000, 0XFFFFFFFF0000FFFF),
    vec128_set2x(0XFFFF000000000000, 0X00000000FFFF0000),
    vec128_set2x(0XFFFF000000000000, 0XFFFF0000FFFFFFFF),
    vec128_set2x(0XFFFF000000000000, 0XFFFF0000FFFFFFFF),
    vec128_set2x(0XFFFFFFFF00000000, 0X0000000000000000)
}, {
    vec128_set2x(0X00000000FFFFFFFF, 0X0000000000000000),
    vec128_set2x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec128_set2x(0XFFFFFFFF00000000, 0X0000000000000000),
    vec128_set2x(0X0000000000000000, 0X0000000000000000),
    vec128_set2x(0XFFFFFFFF00000000, 0X00000000FFFFFFFF),
    vec128_set2x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec128_set2x(0XFFFFFFFF00000000, 0X0000000000000000),
    vec128_set2x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec128_set2x(0XFFFFFFFF00000000, 0X00000000FFFFFFFF),
    vec128_set2x(0X0000000000000000, 0XFFFFFFFF00000000),
    vec128_set2x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec128_set2x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec128_set2x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000)
}

//64
{
    vec256_set4x(0X6969969669699696, 0X6969969669699696, 0X6969969669699696, 0X6969969669699696),
    vec256_set4x(0X9966669966999966, 0X9966669966999966, 0X9966669966999966, 0X9966669966999966),
    vec256_set4x(0X9966669966999966, 0X9966669966999966, 0X9966669966999966, 0X9966669966999966),
    vec256_set4x(0XFF0000FF00FFFF00, 0XFF0000FF00FFFF00, 0XFF0000FF00FFFF00, 0XFF0000FF00FFFF00),
    vec256_set4x(0XCC3333CCCC3333<PERSON>, 0XCC3333CCCC3333CC, 0XCC33<PERSON><PERSON><PERSON>3333<PERSON>, 0XCC3333CCCC3333CC),
    vec256_set4x(0X9966669966999966, 0X9966669966999966, 0X9966669966999966, 0X9966669966999966),
    vec256_set4x(0X6666666666666666, 0X6666666666666666, 0X6666666666666666, 0X6666666666666666),
    vec256_set4x(0XA55AA55AA55AA55A, 0XA55AA55AA55AA55A, 0XA55AA55AA55AA55A, 0XA55AA55AA55AA55A),
    vec256_set4x(0XCCCC33333333CCCC, 0XCCCC33333333CCCC, 0XCCCC33333333CCCC, 0XCCCC33333333CCCC),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X55AAAA55AA5555AA, 0X55AAAA55AA5555AA, 0X55AAAA55AA5555AA, 0X55AAAA55AA5555AA),
    vec256_set4x(0X0FF0F00FF00F0FF0, 0X0FF0F00FF00F0FF0, 0X0FF0F00FF00F0FF0, 0X0FF0F00FF00F0FF0),
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A)
},
//128
{
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X9999999966666666, 0X6666666699999999, 0X9999999966666666, 0X6666666699999999),
    vec256_set4x(0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3, 0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XCC33CC3333CC33CC, 0X33CC33CCCC33CC33, 0XCC33CC3333CC33CC, 0X33CC33CCCC33CC33),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X3C3C3C3C3C3C3C3C, 0X3C3C3C3C3C3C3C3C, 0X3C3C3C3C3C3C3C3C, 0X3C3C3C3C3C3C3C3C),
    vec256_set4x(0XAA5555AAAA5555AA, 0XAA5555AAAA5555AA, 0XAA5555AAAA5555AA, 0XAA5555AAAA5555AA),
    vec256_set4x(0XC33C3CC33CC3C33C, 0XC33C3CC33CC3C33C, 0XC33C3CC33CC3C33C, 0XC33C3CC33CC3C33C),
    vec256_set4x(0X00FFFF0000FFFF00, 0XFF0000FFFF0000FF, 0X00FFFF0000FFFF00, 0XFF0000FFFF0000FF)
},
//256
{
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X0FF0F00FF00F0FF0, 0X0FF0F00FF00F0FF0, 0XF00F0FF00FF0F00F, 0XF00F0FF00FF0F00F),
    vec256_set4x(0X9669966969966996, 0X6996699696699669, 0X6996699696699669, 0X9669966969966996),
    vec256_set4x(0X0000FFFFFFFF0000, 0XFFFF00000000FFFF, 0X0000FFFFFFFF0000, 0XFFFF00000000FFFF),
    vec256_set4x(0X33333333CCCCCCCC, 0X33333333CCCCCCCC, 0X33333333CCCCCCCC, 0X33333333CCCCCCCC),
    vec256_set4x(0XA55A5AA55AA5A55A, 0X5AA5A55AA55A5AA5, 0X5AA5A55AA55A5AA5, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X00FFFF0000FFFF00, 0XFF0000FFFF0000FF, 0XFF0000FFFF0000FF, 0X00FFFF0000FFFF00),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000),
    vec256_set4x(0XC33CC33CC33CC33C, 0XC33CC33CC33CC33C, 0XC33CC33CC33CC33C, 0XC33CC33CC33CC33C),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555)
},
//512
{
    vec256_set4x(0XC33C3CC33CC3C33C, 0X3CC3C33CC33C3CC3, 0XC33C3CC33CC3C33C, 0X3CC3C33CC33C3CC3),
    vec256_set4x(0X9966669966999966, 0X6699996699666699, 0X9966669966999966, 0X6699996699666699),
    vec256_set4x(0X9966996699669966, 0X6699669966996699, 0X6699669966996699, 0X9966996699669966),
    vec256_set4x(0X6969969669699696, 0X6969969669699696, 0X6969969669699696, 0X6969969669699696),
    vec256_set4x(0XAA55AA5555AA55AA, 0XAA55AA5555AA55AA, 0XAA55AA5555AA55AA, 0XAA55AA5555AA55AA),
    vec256_set4x(0X9966996699669966, 0X9966996699669966, 0X6699669966996699, 0X6699669966996699),
    vec256_set4x(0X5AA5A55A5AA5A55A, 0XA55A5AA5A55A5AA5, 0X5AA5A55A5AA5A55A, 0XA55A5AA5A55A5AA5),
    vec256_set4x(0XC3C3C3C33C3C3C3C, 0XC3C3C3C33C3C3C3C, 0X3C3C3C3CC3C3C3C3, 0X3C3C3C3CC3C3C3C3),
    vec256_set4x(0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3),
    vec256_set4x(0X3333CCCC3333CCCC, 0X3333CCCC3333CCCC, 0XCCCC3333CCCC3333, 0XCCCC3333CCCC3333),
    vec256_set4x(0X9999999966666666, 0X6666666699999999, 0X6666666699999999, 0X9999999966666666),
    vec256_set4x(0XC33CC33CC33CC33C, 0X3CC33CC33CC33CC3, 0XC33CC33CC33CC33C, 0X3CC33CC33CC33CC3),
    vec256_set4x(0X6666999999996666, 0X9999666666669999, 0X9999666666669999, 0X6666999999996666)
}, {
    vec256_set4x(0XC33C3CC33CC3C33C, 0X3CC3C33CC33C3CC3, 0XC33C3CC33CC3C33C, 0X3CC3C33CC33C3CC3),
    vec256_set4x(0X6699996699666699, 0X9966669966999966, 0X6699996699666699, 0X9966669966999966),
    vec256_set4x(0X6699669966996699, 0X9966996699669966, 0X9966996699669966, 0X6699669966996699),
    vec256_set4x(0X6969969669699696, 0X6969969669699696, 0X6969969669699696, 0X6969969669699696),
    vec256_set4x(0X55AA55AAAA55AA55, 0X55AA55AAAA55AA55, 0X55AA55AAAA55AA55, 0X55AA55AAAA55AA55),
    vec256_set4x(0X9966996699669966, 0X9966996699669966, 0X6699669966996699, 0X6699669966996699),
    vec256_set4x(0X5AA5A55A5AA5A55A, 0XA55A5AA5A55A5AA5, 0X5AA5A55A5AA5A55A, 0XA55A5AA5A55A5AA5),
    vec256_set4x(0XC3C3C3C33C3C3C3C, 0XC3C3C3C33C3C3C3C, 0X3C3C3C3CC3C3C3C3, 0X3C3C3C3CC3C3C3C3),
    vec256_set4x(0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C),
    vec256_set4x(0X3333CCCC3333CCCC, 0X3333CCCC3333CCCC, 0XCCCC3333CCCC3333, 0XCCCC3333CCCC3333),
    vec256_set4x(0X9999999966666666, 0X6666666699999999, 0X6666666699999999, 0X9999999966666666),
    vec256_set4x(0XC33CC33CC33CC33C, 0X3CC33CC33CC33CC3, 0XC33CC33CC33CC33C, 0X3CC33CC33CC33CC3),
    vec256_set4x(0X6666999999996666, 0X9999666666669999, 0X9999666666669999, 0X6666999999996666)
},
//1024
{
    vec256_set4x(0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3, 0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3),
    vec256_set4x(0X55555555AAAAAAAA, 0X55555555AAAAAAAA, 0X55555555AAAAAAAA, 0X55555555AAAAAAAA),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X55AAAA55AA5555AA, 0X55AAAA55AA5555AA, 0XAA5555AA55AAAA55, 0XAA5555AA55AAAA55),
    vec256_set4x(0XF00F0FF0F00F0FF0, 0XF00F0FF0F00F0FF0, 0X0FF0F00F0FF0F00F, 0X0FF0F00F0FF0F00F),
    vec256_set4x(0X9669699696696996, 0X9669699696696996, 0X9669699696696996, 0X9669699696696996),
    vec256_set4x(0XA55AA55AA55AA55A, 0X5AA55AA55AA55AA5, 0X5AA55AA55AA55AA5, 0XA55AA55AA55AA55A),
    vec256_set4x(0X55555555AAAAAAAA, 0X55555555AAAAAAAA, 0XAAAAAAAA55555555, 0XAAAAAAAA55555555),
    vec256_set4x(0XCCCC33333333CCCC, 0X3333CCCCCCCC3333, 0X3333CCCCCCCC3333, 0XCCCC33333333CCCC),
    vec256_set4x(0X0000FFFFFFFF0000, 0X0000FFFFFFFF0000, 0XFFFF00000000FFFF, 0XFFFF00000000FFFF),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996699669966996, 0X9669966996699669, 0X9669966996699669, 0X6996699669966996)
}, {
    vec256_set4x(0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3, 0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3),
    vec256_set4x(0X55555555AAAAAAAA, 0X55555555AAAAAAAA, 0X55555555AAAAAAAA, 0X55555555AAAAAAAA),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XAA5555AA55AAAA55, 0XAA5555AA55AAAA55, 0X55AAAA55AA5555AA, 0X55AAAA55AA5555AA),
    vec256_set4x(0X0FF0F00F0FF0F00F, 0X0FF0F00F0FF0F00F, 0XF00F0FF0F00F0FF0, 0XF00F0FF0F00F0FF0),
    vec256_set4x(0X6996966969969669, 0X6996966969969669, 0X6996966969969669, 0X6996966969969669),
    vec256_set4x(0XA55AA55AA55AA55A, 0X5AA55AA55AA55AA5, 0X5AA55AA55AA55AA5, 0XA55AA55AA55AA55A),
    vec256_set4x(0XAAAAAAAA55555555, 0XAAAAAAAA55555555, 0X55555555AAAAAAAA, 0X55555555AAAAAAAA),
    vec256_set4x(0XCCCC33333333CCCC, 0X3333CCCCCCCC3333, 0X3333CCCCCCCC3333, 0XCCCC33333333CCCC),
    vec256_set4x(0X0000FFFFFFFF0000, 0X0000FFFFFFFF0000, 0XFFFF00000000FFFF, 0XFFFF00000000FFFF),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996699669966996, 0X9669966996699669, 0X9669966996699669, 0X6996699669966996)
}, {
    vec256_set4x(0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3, 0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3),
    vec256_set4x(0XAAAAAAAA55555555, 0XAAAAAAAA55555555, 0XAAAAAAAA55555555, 0XAAAAAAAA55555555),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XAA5555AA55AAAA55, 0XAA5555AA55AAAA55, 0X55AAAA55AA5555AA, 0X55AAAA55AA5555AA),
    vec256_set4x(0XF00F0FF0F00F0FF0, 0XF00F0FF0F00F0FF0, 0X0FF0F00F0FF0F00F, 0X0FF0F00F0FF0F00F),
    vec256_set4x(0X9669699696696996, 0X9669699696696996, 0X9669699696696996, 0X9669699696696996),
    vec256_set4x(0XA55AA55AA55AA55A, 0X5AA55AA55AA55AA5, 0X5AA55AA55AA55AA5, 0XA55AA55AA55AA55A),
    vec256_set4x(0X55555555AAAAAAAA, 0X55555555AAAAAAAA, 0XAAAAAAAA55555555, 0XAAAAAAAA55555555),
    vec256_set4x(0XCCCC33333333CCCC, 0X3333CCCCCCCC3333, 0X3333CCCCCCCC3333, 0XCCCC33333333CCCC),
    vec256_set4x(0X0000FFFFFFFF0000, 0X0000FFFFFFFF0000, 0XFFFF00000000FFFF, 0XFFFF00000000FFFF),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996699669966996, 0X9669966996699669, 0X9669966996699669, 0X6996699669966996)
}, {
    vec256_set4x(0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3, 0X3C3CC3C3C3C33C3C, 0XC3C33C3C3C3CC3C3),
    vec256_set4x(0XAAAAAAAA55555555, 0XAAAAAAAA55555555, 0XAAAAAAAA55555555, 0XAAAAAAAA55555555),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X55AAAA55AA5555AA, 0X55AAAA55AA5555AA, 0XAA5555AA55AAAA55, 0XAA5555AA55AAAA55),
    vec256_set4x(0X0FF0F00F0FF0F00F, 0X0FF0F00F0FF0F00F, 0XF00F0FF0F00F0FF0, 0XF00F0FF0F00F0FF0),
    vec256_set4x(0X6996966969969669, 0X6996966969969669, 0X6996966969969669, 0X6996966969969669),
    vec256_set4x(0XA55AA55AA55AA55A, 0X5AA55AA55AA55AA5, 0X5AA55AA55AA55AA5, 0XA55AA55AA55AA55A),
    vec256_set4x(0XAAAAAAAA55555555, 0XAAAAAAAA55555555, 0X55555555AAAAAAAA, 0X55555555AAAAAAAA),
    vec256_set4x(0XCCCC33333333CCCC, 0X3333CCCCCCCC3333, 0X3333CCCCCCCC3333, 0XCCCC33333333CCCC),
    vec256_set4x(0X0000FFFFFFFF0000, 0X0000FFFFFFFF0000, 0XFFFF00000000FFFF, 0XFFFF00000000FFFF),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996699669966996, 0X9669966996699669, 0X9669966996699669, 0X6996699669966996)
},
//2048
{
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0X55AA55AA55AA55AA, 0X55AA55AA55AA55AA, 0XAA55AA55AA55AA55, 0XAA55AA55AA55AA55),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X0F0F0F0FF0F0F0F0, 0XF0F0F0F00F0F0F0F, 0XF0F0F0F00F0F0F0F, 0X0F0F0F0FF0F0F0F0),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00),
    vec256_set4x(0X33CCCC33CC3333CC, 0XCC3333CC33CCCC33, 0XCC3333CC33CCCC33, 0X33CCCC33CC3333CC),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996966996696996, 0X9669699669969669, 0X6996966996696996, 0X9669699669969669),
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6996966996696996, 0X6996966996696996, 0X6996966996696996, 0X6996966996696996)
}, {
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3),
    vec256_set4x(0XAA55AA55AA55AA55, 0XAA55AA55AA55AA55, 0X55AA55AA55AA55AA, 0X55AA55AA55AA55AA),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XF0F0F0F00F0F0F0F, 0X0F0F0F0FF0F0F0F0, 0X0F0F0F0FF0F0F0F0, 0XF0F0F0F00F0F0F0F),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00),
    vec256_set4x(0X33CCCC33CC3333CC, 0XCC3333CC33CCCC33, 0XCC3333CC33CCCC33, 0X33CCCC33CC3333CC),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996966996696996, 0X9669699669969669, 0X6996966996696996, 0X9669699669969669),
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6996966996696996, 0X6996966996696996, 0X6996966996696996, 0X6996966996696996)
}, {
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA),
    vec256_set4x(0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF),
    vec256_set4x(0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3),
    vec256_set4x(0XAA55AA55AA55AA55, 0XAA55AA55AA55AA55, 0X55AA55AA55AA55AA, 0X55AA55AA55AA55AA),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X0F0F0F0FF0F0F0F0, 0XF0F0F0F00F0F0F0F, 0XF0F0F0F00F0F0F0F, 0X0F0F0F0FF0F0F0F0),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00),
    vec256_set4x(0X33CCCC33CC3333CC, 0XCC3333CC33CCCC33, 0XCC3333CC33CCCC33, 0X33CCCC33CC3333CC),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996966996696996, 0X9669699669969669, 0X6996966996696996, 0X9669699669969669),
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6996966996696996, 0X6996966996696996, 0X6996966996696996, 0X6996966996696996)
}, {
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA),
    vec256_set4x(0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF),
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0X55AA55AA55AA55AA, 0X55AA55AA55AA55AA, 0XAA55AA55AA55AA55, 0XAA55AA55AA55AA55),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XF0F0F0F00F0F0F0F, 0X0F0F0F0FF0F0F0F0, 0X0F0F0F0FF0F0F0F0, 0XF0F0F0F00F0F0F0F),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00),
    vec256_set4x(0X33CCCC33CC3333CC, 0XCC3333CC33CCCC33, 0XCC3333CC33CCCC33, 0X33CCCC33CC3333CC),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996966996696996, 0X9669699669969669, 0X6996966996696996, 0X9669699669969669),
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6996966996696996, 0X6996966996696996, 0X6996966996696996, 0X6996966996696996)
}, {
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0X5555555555555555, 0X5555555555555555, 0X5555555555555555, 0X5555555555555555),
    vec256_set4x(0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF),
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0X55AA55AA55AA55AA, 0X55AA55AA55AA55AA, 0XAA55AA55AA55AA55, 0XAA55AA55AA55AA55),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X0F0F0F0FF0F0F0F0, 0XF0F0F0F00F0F0F0F, 0XF0F0F0F00F0F0F0F, 0X0F0F0F0FF0F0F0F0),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00),
    vec256_set4x(0X33CCCC33CC3333CC, 0XCC3333CC33CCCC33, 0XCC3333CC33CCCC33, 0X33CCCC33CC3333CC),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996966996696996, 0X9669699669969669, 0X6996966996696996, 0X9669699669969669),
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6996966996696996, 0X6996966996696996, 0X6996966996696996, 0X6996966996696996)
}, {
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0X5555555555555555, 0X5555555555555555, 0X5555555555555555, 0X5555555555555555),
    vec256_set4x(0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF),
    vec256_set4x(0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3),
    vec256_set4x(0XAA55AA55AA55AA55, 0XAA55AA55AA55AA55, 0X55AA55AA55AA55AA, 0X55AA55AA55AA55AA),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XF0F0F0F00F0F0F0F, 0X0F0F0F0FF0F0F0F0, 0X0F0F0F0FF0F0F0F0, 0XF0F0F0F00F0F0F0F),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00),
    vec256_set4x(0X33CCCC33CC3333CC, 0XCC3333CC33CCCC33, 0XCC3333CC33CCCC33, 0X33CCCC33CC3333CC),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996966996696996, 0X9669699669969669, 0X6996966996696996, 0X9669699669969669),
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6996966996696996, 0X6996966996696996, 0X6996966996696996, 0X6996966996696996)
}, {
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0X5555555555555555, 0X5555555555555555, 0X5555555555555555, 0X5555555555555555),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3, 0XC33C3CC3C33C3CC3),
    vec256_set4x(0XAA55AA55AA55AA55, 0XAA55AA55AA55AA55, 0X55AA55AA55AA55AA, 0X55AA55AA55AA55AA),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X0F0F0F0FF0F0F0F0, 0XF0F0F0F00F0F0F0F, 0XF0F0F0F00F0F0F0F, 0X0F0F0F0FF0F0F0F0),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00),
    vec256_set4x(0X33CCCC33CC3333CC, 0XCC3333CC33CCCC33, 0XCC3333CC33CCCC33, 0X33CCCC33CC3333CC),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996966996696996, 0X9669699669969669, 0X6996966996696996, 0X9669699669969669),
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6996966996696996, 0X6996966996696996, 0X6996966996696996, 0X6996966996696996)
}, {
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0X5555555555555555, 0X5555555555555555, 0X5555555555555555, 0X5555555555555555),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C, 0X3CC3C33C3CC3C33C),
    vec256_set4x(0X55AA55AA55AA55AA, 0X55AA55AA55AA55AA, 0XAA55AA55AA55AA55, 0XAA55AA55AA55AA55),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XF0F0F0F00F0F0F0F, 0X0F0F0F0FF0F0F0F0, 0X0F0F0F0FF0F0F0F0, 0XF0F0F0F00F0F0F0F),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00),
    vec256_set4x(0X33CCCC33CC3333CC, 0XCC3333CC33CCCC33, 0XCC3333CC33CCCC33, 0X33CCCC33CC3333CC),
    vec256_set4x(0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF, 0XFF0000FF00FFFF00, 0X00FFFF00FF0000FF),
    vec256_set4x(0X6996966996696996, 0X9669699669969669, 0X6996966996696996, 0X9669699669969669),
    vec256_set4x(0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A, 0XA55A5AA55AA5A55A),
    vec256_set4x(0X6996966996696996, 0X6996966996696996, 0X6996966996696996, 0X6996966996696996)
},
//4096
{
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}, {
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000),
    vec256_set4x(0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00, 0XFF00FF00FF00FF00),
    vec256_set4x(0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0, 0XF0F0F0F0F0F0F0F0),
    vec256_set4x(0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC, 0XCCCCCCCCCCCCCCCC),
    vec256_set4x(0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA, 0XAAAAAAAAAAAAAAAA)
}

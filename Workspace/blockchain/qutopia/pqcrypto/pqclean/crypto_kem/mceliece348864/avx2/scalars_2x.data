{
    vec128_set2x(0xf3cfc030fc30f003, 0x000c03c0c3c0330c),
    vec128_set2x(0x3fcf0f003c00c00c, 0xf330cffcc00f33c0),
    vec128_set2x(0x30033cc300c0c03c, 0xccf330f00f3c0333),
    vec128_set2x(0xccff0f3c0f30f0c0, 0xff03fff3ff0cf0c0),
    vec128_set2x(0x0300c03ff303c3f0, 0x3cc3fcf00fcc303c),
    vec128_set2x(0x3fff3c0ff0ccccc0, 0x0f000c0fc30303f3),
    vec128_set2x(0xf3fff0c00f3c3cc0, 0xcf0fc3ff333ccf3c),
    vec128_set2x(0x3003333fffc3c000, 0x003f3fc3c0ff333f),
    vec128_set2x(0x0ff30fffc3fff300, 0x3cc3f0f3cf0ff00f),
    vec128_set2x(0xffc0f300f0f0cc00, 0xf3f33cc03fc30cc0),
    vec128_set2x(0xc0cff3fccc3cfc00, 0x3cc330cfc333f33f),
    vec128_set2x(0xfc3c03f0f330c000, 0x3cc0303ff3c3fffc),
}, {
    vec128_set2x(0x000f00000000f00f, 0x0f00f00f00000000),
    vec128_set2x(0x00000f00f00000f0, 0xf00000000000f000),
    vec128_set2x(0x0f00000f00000f00, 0x00000f00000000f0),
    vec128_set2x(0xf00f00f00f000000, 0x0f00f00000f00000),
    vec128_set2x(0x00f00000000000f0, 0x000f00000f00f00f),
    vec128_set2x(0x0000000f00000000, 0x00f00f00f00f0000),
    vec128_set2x(0xf00000000f00f000, 0x0f00f00000000000),
    vec128_set2x(0x00f00f00000f0000, 0x000000000f000000),
    vec128_set2x(0x0000f00000f00f00, 0x00f00000000f00f0),
    vec128_set2x(0x000f00f00f00f000, 0x0000f00f00000f00),
    vec128_set2x(0x00f00f0000000000, 0xf00000f00000f00f),
    vec128_set2x(0x0000000000f00000, 0x00000f00f00f00f0),
}, {
    vec128_set2x(0x0000ff00ff0000ff, 0xff00ffffff000000),
    vec128_set2x(0x0000ff000000ff00, 0xff0000ffff000000),
    vec128_set2x(0xff0000ff00ff0000, 0xffff00ffff000000),
    vec128_set2x(0xffff0000ff000000, 0xff00ffffffffff00),
    vec128_set2x(0x00ff00ff00ff0000, 0x00000000ff00ff00),
    vec128_set2x(0x0000ffffff000000, 0xffffffff00ff0000),
    vec128_set2x(0x00ffff00ff000000, 0x00ffffff00ff0000),
    vec128_set2x(0xffffff0000ff0000, 0xffff00ffff00ffff),
    vec128_set2x(0xffff00ffff00ff00, 0xffff0000ffffffff),
    vec128_set2x(0x0000ff0000000000, 0xff00000000ff0000),
    vec128_set2x(0xffffff00ff000000, 0x000000ff00ff00ff),
    vec128_set2x(0x00ff000000000000, 0x00ff00ff00ffff00),
}, {
    vec128_set2x(0x000000000000ffff, 0x0000000000000000),
    vec128_set2x(0x00000000ffff0000, 0xffff000000000000),
    vec128_set2x(0x0000000000000000, 0x0000000000000000),
    vec128_set2x(0xffff000000000000, 0x0000000000000000),
    vec128_set2x(0x00000000ffff0000, 0xffff00000000ffff),
    vec128_set2x(0x0000ffff00000000, 0x0000000000000000),
    vec128_set2x(0x0000000000000000, 0x0000ffff00000000),
    vec128_set2x(0x00000000ffff0000, 0xffff00000000ffff),
    vec128_set2x(0x0000ffff00000000, 0x00000000ffff0000),
    vec128_set2x(0x0000000000000000, 0x0000000000000000),
    vec128_set2x(0x0000000000000000, 0xffff00000000ffff),
    vec128_set2x(0x0000000000000000, 0x00000000ffff0000),
}, {
    vec128_set2x(0x00000000ffffffff, 0x0000000000000000),
    vec128_set2x(0xffffffff00000000, 0x0000000000000000),
    vec128_set2x(0xffffffff00000000, 0x00000000ffffffff),
    vec128_set2x(0x0000000000000000, 0xffffffff00000000),
    vec128_set2x(0x0000000000000000, 0xffffffff00000000),
    vec128_set2x(0xffffffff00000000, 0x0000000000000000),
    vec128_set2x(0x0000000000000000, 0xffffffff00000000),
    vec128_set2x(0x0000000000000000, 0xffffffffffffffff),
    vec128_set2x(0xffffffff00000000, 0xffffffff00000000),
    vec128_set2x(0x0000000000000000, 0x0000000000000000),
    vec128_set2x(0x0000000000000000, 0xffffffffffffffff),
    vec128_set2x(0x0000000000000000, 0xffffffff00000000),
},

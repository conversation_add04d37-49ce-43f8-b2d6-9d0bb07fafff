{
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC),
    vec256_set4x(0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555),
}, {
    vec256_set4x(0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC),
    vec256_set4x(0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555),
}, {
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333),
    vec256_set4x(0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555),
}, {
    vec256_set4x(0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333),
    vec256_set4x(0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555),
}, {
    vec256_set4x(0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC),
    vec256_set4x(0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555),
}, {
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC, 0x3333CCCC3333CCCC),
    vec256_set4x(0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555),
}, {
    vec256_set4x(0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333),
    vec256_set4x(0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555),
}, {
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0, 0x0F0F0F0FF0F0F0F0),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333, 0xCCCC3333CCCC3333),
    vec256_set4x(0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555),
},

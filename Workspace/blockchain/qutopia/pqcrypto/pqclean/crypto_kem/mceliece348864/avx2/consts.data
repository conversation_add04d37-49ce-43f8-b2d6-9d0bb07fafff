{
    vec256_set4x(0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0),
    vec256_set4x(0xF0F00F0F0F0FF0F0, 0xF0F00F0F0F0FF0F0, 0xF0F00F0F0F0FF0F0, 0xF0F00F0F0F0FF0F0),
    vec256_set4x(0x0FF00FF00FF00FF0, 0x0FF00FF00FF00FF0, 0x0FF00FF00FF00FF0, 0x0FF00FF00FF00FF0),
    vec256_set4x(0xAA5555AAAA5555AA, 0xAA5555AAAA5555AA, 0xAA5555AAAA5555AA, 0xAA5555AAAA5555AA),
    vec256_set4x(0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0),
    vec256_set4x(0x33CCCC33CC3333CC, 0x33CCCC33CC3333CC, 0x33CCCC33CC3333CC, 0x33CCCC33CC3333CC),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xCC33CC3333CC33CC, 0xCC33CC3333CC33CC, 0xCC33CC3333CC33CC, 0xCC33CC3333CC33CC),
    vec256_set4x(0x33CC33CC33CC33CC, 0x33CC33CC33CC33CC, 0x33CC33CC33CC33CC, 0x33CC33CC33CC33CC),
    vec256_set4x(0x5A5A5A5A5A5A5A5A, 0x5A5A5A5A5A5A5A5A, 0x5A5A5A5A5A5A5A5A, 0x5A5A5A5A5A5A5A5A),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0, 0xF00F0FF0F00F0FF0),
}, {
    vec256_set4x(0x3C3C3C3C3C3C3C3C, 0x3C3C3C3C3C3C3C3C, 0x3C3C3C3C3C3C3C3C, 0x3C3C3C3C3C3C3C3C),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F, 0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x5555AAAA5555AAAA, 0xAAAA5555AAAA5555, 0x5555AAAA5555AAAA, 0xAAAA5555AAAA5555),
    vec256_set4x(0xCC3333CCCC3333CC, 0xCC3333CCCC3333CC, 0xCC3333CCCC3333CC, 0xCC3333CCCC3333CC),
    vec256_set4x(0xC33CC33CC33CC33C, 0xC33CC33CC33CC33C, 0xC33CC33CC33CC33C, 0xC33CC33CC33CC33C),
    vec256_set4x(0x55555555AAAAAAAA, 0x55555555AAAAAAAA, 0x55555555AAAAAAAA, 0x55555555AAAAAAAA),
    vec256_set4x(0x33333333CCCCCCCC, 0x33333333CCCCCCCC, 0x33333333CCCCCCCC, 0x33333333CCCCCCCC),
    vec256_set4x(0x00FF00FFFF00FF00, 0xFF00FF0000FF00FF, 0x00FF00FFFF00FF00, 0xFF00FF0000FF00FF),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F, 0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000FFFFFFFF0000, 0x0000FFFFFFFF0000, 0x0000FFFFFFFF0000, 0x0000FFFFFFFF0000),
    vec256_set4x(0xF0F00F0F0F0FF0F0, 0xF0F00F0F0F0FF0F0, 0xF0F00F0F0F0FF0F0, 0xF0F00F0F0F0FF0F0),
}, {
    vec256_set4x(0xAA55AA5555AA55AA, 0x55AA55AAAA55AA55, 0xAA55AA5555AA55AA, 0x55AA55AAAA55AA55),
    vec256_set4x(0xCC33CC3333CC33CC, 0xCC33CC3333CC33CC, 0x33CC33CCCC33CC33, 0x33CC33CCCC33CC33),
    vec256_set4x(0x33CCCC33CC3333CC, 0xCC3333CC33CCCC33, 0xCC3333CC33CCCC33, 0x33CCCC33CC3333CC),
    vec256_set4x(0x55555555AAAAAAAA, 0x55555555AAAAAAAA, 0x55555555AAAAAAAA, 0x55555555AAAAAAAA),
    vec256_set4x(0xFF0000FF00FFFF00, 0xFF0000FF00FFFF00, 0x00FFFF00FF0000FF, 0x00FFFF00FF0000FF),
    vec256_set4x(0x3CC33CC3C33CC33C, 0xC33CC33C3CC33CC3, 0x3CC33CC3C33CC33C, 0xC33CC33C3CC33CC3),
    vec256_set4x(0x5555AAAA5555AAAA, 0xAAAA5555AAAA5555, 0x5555AAAA5555AAAA, 0xAAAA5555AAAA5555),
    vec256_set4x(0x0FF00FF00FF00FF0, 0xF00FF00FF00FF00F, 0x0FF00FF00FF00FF0, 0xF00FF00FF00FF00F),
    vec256_set4x(0xCCCC33333333CCCC, 0x3333CCCCCCCC3333, 0x3333CCCCCCCC3333, 0xCCCC33333333CCCC),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F, 0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0x00FFFF0000FFFF00, 0xFF0000FFFF0000FF, 0x00FFFF0000FFFF00, 0xFF0000FFFF0000FF),
    vec256_set4x(0xC33CC33CC33CC33C, 0xC33CC33CC33CC33C, 0xC33CC33CC33CC33C, 0xC33CC33CC33CC33C),
}, {
    vec256_set4x(0x6699669999669966, 0x9966996666996699, 0x6699669999669966, 0x9966996666996699),
    vec256_set4x(0x33CCCC33CC3333CC, 0x33CCCC33CC3333CC, 0x33CCCC33CC3333CC, 0x33CCCC33CC3333CC),
    vec256_set4x(0xA5A5A5A55A5A5A5A, 0xA5A5A5A55A5A5A5A, 0x5A5A5A5AA5A5A5A5, 0x5A5A5A5AA5A5A5A5),
    vec256_set4x(0x3C3CC3C3C3C33C3C, 0x3C3CC3C3C3C33C3C, 0xC3C33C3C3C3CC3C3, 0xC3C33C3C3C3CC3C3),
    vec256_set4x(0xF00FF00F0FF00FF0, 0x0FF00FF0F00FF00F, 0x0FF00FF0F00FF00F, 0xF00FF00F0FF00FF0),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x3C3CC3C3C3C33C3C, 0x3C3CC3C3C3C33C3C, 0xC3C33C3C3C3CC3C3, 0xC3C33C3C3C3CC3C3),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0xF0F0F0F00F0F0F0F, 0x0F0F0F0FF0F0F0F0, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x33CCCC33CC3333CC, 0xCC3333CC33CCCC33, 0x33CCCC33CC3333CC, 0xCC3333CC33CCCC33),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F, 0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A),
}, {
    vec256_set4x(0x6699669999669966, 0x9966996666996699, 0x6699669999669966, 0x9966996666996699),
    vec256_set4x(0xCC3333CC33CCCC33, 0xCC3333CC33CCCC33, 0xCC3333CC33CCCC33, 0xCC3333CC33CCCC33),
    vec256_set4x(0x5A5A5A5AA5A5A5A5, 0x5A5A5A5AA5A5A5A5, 0xA5A5A5A55A5A5A5A, 0xA5A5A5A55A5A5A5A),
    vec256_set4x(0x3C3CC3C3C3C33C3C, 0x3C3CC3C3C3C33C3C, 0xC3C33C3C3C3CC3C3, 0xC3C33C3C3C3CC3C3),
    vec256_set4x(0x0FF00FF0F00FF00F, 0xF00FF00F0FF00FF0, 0xF00FF00F0FF00FF0, 0x0FF00FF0F00FF00F),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x3C3CC3C3C3C33C3C, 0x3C3CC3C3C3C33C3C, 0xC3C33C3C3C3CC3C3, 0xC3C33C3C3C3CC3C3),
    vec256_set4x(0x0F0F0F0FF0F0F0F0, 0xF0F0F0F00F0F0F0F, 0x0F0F0F0FF0F0F0F0, 0xF0F0F0F00F0F0F0F),
    vec256_set4x(0x55AA55AA55AA55AA, 0xAA55AA55AA55AA55, 0xAA55AA55AA55AA55, 0x55AA55AA55AA55AA),
    vec256_set4x(0x33CCCC33CC3333CC, 0xCC3333CC33CCCC33, 0x33CCCC33CC3333CC, 0xCC3333CC33CCCC33),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F, 0xF0F0F0F0F0F0F0F0, 0x0F0F0F0F0F0F0F0F),
    vec256_set4x(0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A),
}, {
    vec256_set4x(0x9669699696696996, 0x9669699696696996, 0x9669699696696996, 0x9669699696696996),
    vec256_set4x(0x6996699669966996, 0x6996699669966996, 0x6996699669966996, 0x6996699669966996),
    vec256_set4x(0x6996699669966996, 0x6996699669966996, 0x6996699669966996, 0x6996699669966996),
    vec256_set4x(0x00FFFF0000FFFF00, 0x00FFFF0000FFFF00, 0xFF0000FFFF0000FF, 0xFF0000FFFF0000FF),
    vec256_set4x(0xFF00FF00FF00FF00, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF00FF00F0FF00FF0, 0x0FF00FF0F00FF00F, 0x0FF00FF0F00FF00F, 0xF00FF00F0FF00FF0),
    vec256_set4x(0xF0F00F0F0F0FF0F0, 0x0F0FF0F0F0F00F0F, 0x0F0FF0F0F0F00F0F, 0xF0F00F0F0F0FF0F0),
    vec256_set4x(0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3, 0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3),
    vec256_set4x(0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3, 0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3),
    vec256_set4x(0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A),
    vec256_set4x(0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C),
    vec256_set4x(0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C),
}, {
    vec256_set4x(0x9669699696696996, 0x9669699696696996, 0x9669699696696996, 0x9669699696696996),
    vec256_set4x(0x6996699669966996, 0x6996699669966996, 0x6996699669966996, 0x6996699669966996),
    vec256_set4x(0x9669966996699669, 0x9669966996699669, 0x9669966996699669, 0x9669966996699669),
    vec256_set4x(0xFF0000FFFF0000FF, 0xFF0000FFFF0000FF, 0x00FFFF0000FFFF00, 0x00FFFF0000FFFF00),
    vec256_set4x(0x00FF00FF00FF00FF, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF00FF00F0FF00FF0, 0x0FF00FF0F00FF00F, 0x0FF00FF0F00FF00F, 0xF00FF00F0FF00FF0),
    vec256_set4x(0xF0F00F0F0F0FF0F0, 0x0F0FF0F0F0F00F0F, 0x0F0FF0F0F0F00F0F, 0xF0F00F0F0F0FF0F0),
    vec256_set4x(0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3, 0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3),
    vec256_set4x(0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3, 0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3),
    vec256_set4x(0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A),
    vec256_set4x(0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C),
    vec256_set4x(0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C),
}, {
    vec256_set4x(0x9669699696696996, 0x9669699696696996, 0x9669699696696996, 0x9669699696696996),
    vec256_set4x(0x9669966996699669, 0x9669966996699669, 0x9669966996699669, 0x9669966996699669),
    vec256_set4x(0x9669966996699669, 0x9669966996699669, 0x9669966996699669, 0x9669966996699669),
    vec256_set4x(0x00FFFF0000FFFF00, 0x00FFFF0000FFFF00, 0xFF0000FFFF0000FF, 0xFF0000FFFF0000FF),
    vec256_set4x(0xFF00FF00FF00FF00, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF00FF00F0FF00FF0, 0x0FF00FF0F00FF00F, 0x0FF00FF0F00FF00F, 0xF00FF00F0FF00FF0),
    vec256_set4x(0xF0F00F0F0F0FF0F0, 0x0F0FF0F0F0F00F0F, 0x0F0FF0F0F0F00F0F, 0xF0F00F0F0F0FF0F0),
    vec256_set4x(0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3, 0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3),
    vec256_set4x(0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3, 0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3),
    vec256_set4x(0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A),
    vec256_set4x(0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C),
    vec256_set4x(0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C),
}, {
    vec256_set4x(0x9669699696696996, 0x9669699696696996, 0x9669699696696996, 0x9669699696696996),
    vec256_set4x(0x9669966996699669, 0x9669966996699669, 0x9669966996699669, 0x9669966996699669),
    vec256_set4x(0x6996699669966996, 0x6996699669966996, 0x6996699669966996, 0x6996699669966996),
    vec256_set4x(0xFF0000FFFF0000FF, 0xFF0000FFFF0000FF, 0x00FFFF0000FFFF00, 0x00FFFF0000FFFF00),
    vec256_set4x(0x00FF00FF00FF00FF, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0x00FF00FF00FF00FF),
    vec256_set4x(0xF00FF00F0FF00FF0, 0x0FF00FF0F00FF00F, 0x0FF00FF0F00FF00F, 0xF00FF00F0FF00FF0),
    vec256_set4x(0xF0F00F0F0F0FF0F0, 0x0F0FF0F0F0F00F0F, 0x0F0FF0F0F0F00F0F, 0xF0F00F0F0F0FF0F0),
    vec256_set4x(0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3, 0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3),
    vec256_set4x(0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3, 0xC33C3CC33CC3C33C, 0x3CC3C33CC33C3CC3),
    vec256_set4x(0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A, 0xA55A5AA55AA5A55A),
    vec256_set4x(0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C, 0xC33C3CC33CC3C33C),
    vec256_set4x(0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C, 0x3CC3C33C3CC3C33C),
}, {
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
}, {
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0x0000000000000000, 0x0000000000000000),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0x0000000000000000, 0xFFFFFFFFFFFFFFFF, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF),
    vec256_set4x(0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000),
    vec256_set4x(0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000),
    vec256_set4x(0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00),
    vec256_set4x(0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0),
    vec256_set4x(0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC),
    vec256_set4x(0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA),
},

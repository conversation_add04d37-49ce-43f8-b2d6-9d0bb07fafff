#include "namespace.h"

#if defined(__APPLE__)
#define ASM_HIDDEN .private_extern
#else
#define ASM_HIDDEN .hidden
#endif

#define MASK0_0 CRYPTO_NAMESPACE(MASK0_0)
#define _MASK0_0 _CRYPTO_NAMESPACE(MASK0_0)
#define MASK0_1 CRYPTO_NAMESPACE(MASK0_1)
#define _MASK0_1 _CRYPTO_NAMESPACE(MASK0_1)
#define MASK1_0 CRYPTO_NAMESPACE(MASK1_0)
#define _MASK1_0 _CRYPTO_NAMESPACE(MASK1_0)
#define MASK1_1 CRYPTO_NAMESPACE(MASK1_1)
#define _MASK1_1 _CRYPTO_NAMESPACE(MASK1_1)
#define MASK2_0 CRYPTO_NAMESPACE(MASK2_0)
#define _MASK2_0 _CRYPTO_NAMESPACE(MASK2_0)
#define MASK2_1 CRYPTO_NAMESPACE(MASK2_1)
#define _MASK2_1 _CRYPTO_NAMESPACE(MASK2_1)
#define MASK3_0 CRYPTO_NAMESPACE(MASK3_0)
#define _MASK3_0 _CRYPTO_NAMESPACE(MASK3_0)
#define MASK3_1 CRYPTO_NAMESPACE(MASK3_1)
#define _MASK3_1 _CRYPTO_NAMESPACE(MASK3_1)
#define MASK4_0 CRYPTO_NAMESPACE(MASK4_0)
#define _MASK4_0 _CRYPTO_NAMESPACE(MASK4_0)
#define MASK4_1 CRYPTO_NAMESPACE(MASK4_1)
#define _MASK4_1 _CRYPTO_NAMESPACE(MASK4_1)
#define MASK5_0 CRYPTO_NAMESPACE(MASK5_0)
#define _MASK5_0 _CRYPTO_NAMESPACE(MASK5_0)
#define MASK5_1 CRYPTO_NAMESPACE(MASK5_1)
#define _MASK5_1 _CRYPTO_NAMESPACE(MASK5_1)
.data

ASM_HIDDEN MASK0_0
ASM_HIDDEN MASK0_1
ASM_HIDDEN MASK1_0
ASM_HIDDEN MASK1_1
ASM_HIDDEN MASK2_0
ASM_HIDDEN MASK2_1
ASM_HIDDEN MASK3_0
ASM_HIDDEN MASK3_1
ASM_HIDDEN MASK4_0
ASM_HIDDEN MASK4_1
ASM_HIDDEN MASK5_0
ASM_HIDDEN MASK5_1

.globl MASK0_0
.globl MASK0_1
.globl MASK1_0
.globl MASK1_1
.globl MASK2_0
.globl MASK2_1
.globl MASK3_0
.globl MASK3_1
.globl MASK4_0
.globl MASK4_1
.globl MASK5_0
.globl MASK5_1

.p2align 5

MASK0_0:
.quad 0x5555555555555555, 0x5555555555555555, 0x5555555555555555, 0x5555555555555555
MASK0_1:
.quad 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA, 0xAAAAAAAAAAAAAAAA
MASK1_0:
.quad 0x3333333333333333, 0x3333333333333333, 0x3333333333333333, 0x3333333333333333
MASK1_1:
.quad 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC, 0xCCCCCCCCCCCCCCCC
MASK2_0:
.quad 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F, 0x0F0F0F0F0F0F0F0F
MASK2_1:
.quad 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0, 0xF0F0F0F0F0F0F0F0
MASK3_0:
.quad 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF, 0x00FF00FF00FF00FF
MASK3_1:
.quad 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00, 0xFF00FF00FF00FF00
MASK4_0:
.quad 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF, 0x0000FFFF0000FFFF
MASK4_1:
.quad 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000, 0xFFFF0000FFFF0000
MASK5_0:
.quad 0x00000000FFFFFFFF, 0x00000000FFFFFFFF, 0x00000000FFFFFFFF, 0x00000000FFFFFFFF
MASK5_1:
.quad 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000, 0xFFFFFFFF00000000

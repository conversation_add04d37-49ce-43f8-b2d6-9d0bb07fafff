{
    vec256_set4x(0X3C3CF30C0000C003, 0X0C0F0FCF0F0CF330, 0XF0F30C33CF03F03F, 0X3F30CC0C000F3FCC),
    vec256_set4x(0X0CCCC3F333C0000C, 0XF0000FC33C3CCF3C, 0X00F30FC00C3300FF, 0XFC3CF030FC3FFF03),
    vec256_set4x(0X03C33F33FCC0C03C, 0X3C0F3F00C3C300FC, 0XF3CC3CF3F3FCF33F, 0X33FFFCFF0CCF3CC3),
    vec256_set4x(0X0003000F3C03C0C0, 0X3C33CCC0F0F3CC30, 0X3C0FC0FC303C3F3C, 0X003CFF33C3CC30CF),
    vec256_set4x(0XF33FF33030CF03F0, 0XC0CFFFFFCCCC30CC, 0XFC30CF303F3FF00F, 0XCFF3CF33C00F3003),
    vec256_set4x(0X0CF0303300F0CCC0, 0X3FC3F3CCFFFC033F, 0X33300C0CC3300CF3, 0X00F3CC0CF3003CCF),
    vec256_set4x(0XFF3F0C0CC0FF3CC0, 0XFC3030CCCCC0CFCF, 0X3C030CF3F03FF3F3, 0X3C000CFCCC3C3333),
    vec256_set4x(0XCF3CF0FF003FC000, 0X0FCF0C00CCF333C3, 0X3CCC03FCCC3FFC03, 0XF3CF03C0FCF03FF0),
    vec256_set4x(0XC00FF3CF0303F300, 0XCFFCF33000CFF030, 0X033C3C3CF0003FC3, 0X3F3C3CF0C330330C),
    vec256_set4x(0X3CCC0CC00CF0CC00, 0X00CFFCC330F30FCC, 0XFFC0FF00F0FF0F03, 0X33CCFCC0FF0033F0),
    vec256_set4x(0XF30FFC3C3FCCFC00, 0X3CCC3FCCC0F3FFF3, 0XF3F30CF003FCC303, 0X33C300C0F0C003F3),
    vec256_set4x(0X3F0FC3F0CCF0C000, 0XF00F0C3FC003C0FF, 0X30CFCFC3CC0F3000, 0X003FF0003F00C00C),
    vec256_set4x(0X3000FF33CCF0F000, 0X330CCFCC03C0FC33, 0X0CF30CCF3FCFCC0F, 0XCFF3C3033F030FFF)
}, {
    vec256_set4x(0X0F0F0FF0F000000F, 0XF0FFFFFFF0F00F00, 0X0F0F00FF0FF0FFFF, 0XFF0F0F00F000F0FF),
    vec256_set4x(0X00FFFFFFFF0000F0, 0X00FFF0FFFF0000FF, 0XF000F0F00F00FF0F, 0X0FFFFFFFFF00000F),
    vec256_set4x(0XFFFF00FF00000F00, 0X00FF00000F0F0FFF, 0X000FFFF0FFF0FF0F, 0XF0FFFF000F00F0FF),
    vec256_set4x(0XFFF000F00F0FF000, 0XF000F0000F00FF0F, 0X00F00FFF00000FF0, 0X0F0000F00FFF0FFF),
    vec256_set4x(0XFFF0000F0FF000F0, 0XFF000000FFF00000, 0XFFFFF0000FFFF00F, 0X0F0F0F00FF0F000F),
    vec256_set4x(0X00FF000FFF000000, 0XF0FF000FF00F0FF0, 0XFFF0FFF0000FFFF0, 0X000F0F0FFFF0F000),
    vec256_set4x(0XFF0F0FFF0F0FF000, 0X0F0F0F00FF000F0F, 0XF0F0F0000F0F0F00, 0XF0FFFF0F00F0FF0F),
    vec256_set4x(0X0FFF0000000F0000, 0X0F0F00F0F0F0F000, 0X00F000F0F00FFF00, 0X0F0F000F0F00F0FF),
    vec256_set4x(0X00F000F0FFF00F00, 0X00F00F00F00F000F, 0XF0FF0F0FFF00F0FF, 0X0000F0FF00FF0F0F),
    vec256_set4x(0X00F00FF00F00F000, 0X00F0F0F00000FFF0, 0XF0FF0FFFF0F0F0FF, 0X00FFFF0FF0FFF0F0),
    vec256_set4x(0XFFF000F000F00000, 0XFFFFFF0FF00F0FFF, 0X00FFFFFFFFFFFFF0, 0X0000000F00F0FFF0),
    vec256_set4x(0X00F00F000FF00000, 0X0F0FFFF00FFFFFFF, 0X00FFF0F0FF000F0F, 0XF0F00000FF00F0F0),
    vec256_set4x(0X0000FF0F0000F000, 0XFFFF0F0FFF0FFF00, 0X000FFFF0000FFF00, 0X0F0F0FFFFFFFFFFF)
}, {
    vec256_set4x(0X00FF0000000000FF, 0X00FF00FF00FF0000, 0XFFFF00FF00FF00FF, 0XFF0000FFFFFF00FF),
    vec256_set4x(0XFFFFFFFFFF00FF00, 0XFF00FFFF000000FF, 0X00FFFF000000FF00, 0XFFFF0000FFFFFFFF),
    vec256_set4x(0XFF0000FF00FF0000, 0X0000FFFF000000FF, 0XFFFF00FFFFFFFF00, 0XFFFF000000FFFFFF),
    vec256_set4x(0XFFFF000000FF0000, 0X00FFFF00FF000000, 0X0000FFFF00FFFFFF, 0X00FFFF00FF0000FF),
    vec256_set4x(0XFF00000000FF0000, 0XFFFFFF0000FF00FF, 0X00FF0000FF0000FF, 0XFFFFFF00FFFFFF00),
    vec256_set4x(0X00FFFFFFFF000000, 0X0000FFFF00FFFF00, 0XFFFF0000FF00FFFF, 0X00FFFF00FFFF00FF),
    vec256_set4x(0XFF0000FFFFFF0000, 0XFF00FF0000FFFF00, 0XFF000000FFFFFF00, 0X0000FFFF00FF0000),
    vec256_set4x(0XFF00FF00FFFF0000, 0X00000000FFFFFFFF, 0X000000000000FFFF, 0X000000FFFF000000),
    vec256_set4x(0X00FFFFFFFF00FF00, 0X0000FF0000000000, 0XFF00FF00FFFF0000, 0XFF00FF0000FF00FF),
    vec256_set4x(0XFFFF000000000000, 0XFF00FFFF00FFFF00, 0XFFFF00FFFF00FFFF, 0X00FF0000000000FF),
    vec256_set4x(0X00FF0000FF000000, 0X00FFFF00000000FF, 0XFFFFFFFFFF00FF00, 0XFF00FFFF00FF00FF),
    vec256_set4x(0XFF00FF00FF000000, 0X0000FF00FF00FFFF, 0XFFFF00FFFF0000FF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X00FF00FFFF000000, 0XFF0000FFFFFF0000, 0X0000FF00000000FF, 0X0000FF000000FFFF)
}, {
    vec256_set4x(0X000000000000FFFF, 0X0000FFFF00000000, 0XFFFFFFFFFFFFFFFF, 0X0000FFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFF0000, 0XFFFFFFFF0000FFFF, 0XFFFFFFFF00000000, 0X0000FFFF0000FFFF),
    vec256_set4x(0X0000000000000000, 0X00000000FFFFFFFF, 0XFFFF000000000000, 0X0000FFFFFFFF0000),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000000000000000, 0X0000FFFF00000000, 0XFFFF0000FFFFFFFF),
    vec256_set4x(0XFFFFFFFFFFFF0000, 0X0000FFFF00000000, 0X00000000FFFF0000, 0X00000000FFFF0000),
    vec256_set4x(0X0000FFFF00000000, 0XFFFF0000FFFF0000, 0X0000FFFFFFFFFFFF, 0XFFFF00000000FFFF),
    vec256_set4x(0X0000FFFFFFFF0000, 0X0000FFFFFFFF0000, 0X0000FFFFFFFFFFFF, 0X0000FFFF0000FFFF),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0XFFFFFFFF00000000, 0XFFFF00000000FFFF),
    vec256_set4x(0X0000FFFF00000000, 0XFFFFFFFF0000FFFF, 0X000000000000FFFF, 0X0000FFFF0000FFFF),
    vec256_set4x(0XFFFF000000000000, 0X00000000FFFF0000, 0X000000000000FFFF, 0X0000FFFF00000000),
    vec256_set4x(0XFFFF000000000000, 0XFFFF0000FFFFFFFF, 0XFFFFFFFFFFFF0000, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFF000000000000, 0XFFFF0000FFFFFFFF, 0XFFFFFFFF0000FFFF, 0X0000FFFFFFFF0000),
    vec256_set4x(0XFFFFFFFF00000000, 0X0000000000000000, 0XFFFF0000FFFFFFFF, 0X0000FFFFFFFFFFFF)
}, {
    vec256_set4x(0X00000000FFFFFFFF, 0X0000000000000000, 0X00000000FFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFF00000000, 0X00000000FFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0X0000000000000000, 0XFFFFFFFF00000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFF00000000, 0X00000000FFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000),
    vec256_set4x(0XFFFFFFFF00000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFF00000000),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0X00000000FFFFFFFF),
    vec256_set4x(0XFFFFFFFF00000000, 0X00000000FFFFFFFF, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFF00000000, 0X00000000FFFFFFFF, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFF00000000),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFF00000000)
}, {
    vec256_set4x(0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF),
    vec256_set4x(0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF, 0X0000000000000000),
    vec256_set4x(0X0000000000000000, 0X0000000000000000, 0XFFFFFFFFFFFFFFFF, 0XFFFFFFFFFFFFFFFF)
}

{
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0, 0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3, 0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF, 0X00000000FFFFFFFF),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5, 0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A, 0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000, 0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF),
    vec256_set4x(0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33, 0XCC33CC33CC33CC33),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X9696969669696969, 0X9696969669696969, 0X9696969669696969, 0X9696969669696969),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5, 0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}, {
    vec256_set4x(0XA55AA55A5AA55AA5, 0XA55AA55A5AA55AA5, 0X5AA55AA5A55AA55A, 0X5AA55AA5A55AA55A),
    vec256_set4x(0XC33CC33C3CC33CC3, 0X3CC33CC3C33CC33C, 0X3CC33CC3C33CC33C, 0XC33CC33C3CC33CC3),
    vec256_set4x(0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A, 0XA5A55A5AA5A55A5A),
    vec256_set4x(0XFFFF0000FFFF0000, 0X0000FFFF0000FFFF, 0X0000FFFF0000FFFF, 0XFFFF0000FFFF0000),
    vec256_set4x(0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC, 0X33CC33CC33CC33CC),
    vec256_set4x(0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F, 0XF00FF00F0FF00FF0, 0X0FF00FF0F00FF00F),
    vec256_set4x(0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000, 0XFFFFFFFF00000000),
    vec256_set4x(0X6969696996969696, 0X6969696996969696, 0X6969696996969696, 0X6969696996969696),
    vec256_set4x(0X5A5A5A5A5A5A5A5A, 0X5A5A5A5A5A5A5A5A, 0XA5A5A5A5A5A5A5A5, 0XA5A5A5A5A5A5A5A5),
    vec256_set4x(0XA5A5A5A55A5A5A5A, 0XA5A5A5A55A5A5A5A, 0X5A5A5A5AA5A5A5A5, 0X5A5A5A5AA5A5A5A5),
    vec256_set4x(0X5555AAAAAAAA5555, 0XAAAA55555555AAAA, 0X5555AAAAAAAA5555, 0XAAAA55555555AAAA),
    vec256_set4x(0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0, 0XF00FF00FF00FF00F, 0X0FF00FF00FF00FF0),
    vec256_set4x(0X0F0FF0F00F0FF0F0, 0XF0F00F0FF0F00F0F, 0XF0F00F0FF0F00F0F, 0X0F0FF0F00F0FF0F0)
}

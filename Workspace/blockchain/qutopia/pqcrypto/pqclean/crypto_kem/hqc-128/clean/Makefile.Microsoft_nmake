# This Makefile can be used with Microsoft Visual Studio's nmake using the command:
#    nmake /f Makefile.Microsoft_nmake

LIBRARY=libhqc-128_clean.lib
OBJECTS=code.obj fft.obj gf.obj gf2x.obj hqc.obj kem.obj parsing.obj reed_muller.obj reed_solomon.obj shake_ds.obj shake_prng.obj vector.obj 

CFLAGS=/nologo /O2 /I ..\..\..\common /W4 /WX

all: $(LIBRARY)

# Make sure objects are recompiled if headers change.
$(OBJECTS): *.h

$(LIBRARY): $(OBJECTS)
    LIB.EXE /NOLOGO /WX /OUT:$@ $**

clean:
    -DEL $(OBJECTS)
    -DEL $(LIBRARY)

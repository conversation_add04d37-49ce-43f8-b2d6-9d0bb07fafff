![Test hqc-128](https://github.com/PQClean/PQClean/workflows/Test%20hqc-128/badge.svg?branch=master)
![Test hqc-192](https://github.com/PQClean/PQClean/workflows/Test%20hqc-192/badge.svg?branch=master)
![Test hqc-256](https://github.com/PQClean/PQClean/workflows/Test%20hqc-256/badge.svg?branch=master)
![Test mceliece348864](https://github.com/PQClean/PQClean/workflows/Test%20mceliece348864/badge.svg?branch=master)
![Test mceliece348864f](https://github.com/PQClean/PQClean/workflows/Test%20mceliece348864f/badge.svg?branch=master)
![Test mceliece460896](https://github.com/PQClean/PQClean/workflows/Test%20mceliece460896/badge.svg?branch=master)
![Test mceliece460896f](https://github.com/PQClean/PQClean/workflows/Test%20mceliece460896f/badge.svg?branch=master)
![Test mceliece6688128](https://github.com/PQClean/PQClean/workflows/Test%20mceliece6688128/badge.svg?branch=master)
![Test mceliece6688128f](https://github.com/PQClean/PQClean/workflows/Test%20mceliece6688128f/badge.svg?branch=master)
![Test mceliece6960119](https://github.com/PQClean/PQClean/workflows/Test%20mceliece6960119/badge.svg?branch=master)
![Test mceliece6960119f](https://github.com/PQClean/PQClean/workflows/Test%20mceliece6960119f/badge.svg?branch=master)
![Test mceliece8192128](https://github.com/PQClean/PQClean/workflows/Test%20mceliece8192128/badge.svg?branch=master)
![Test mceliece8192128f](https://github.com/PQClean/PQClean/workflows/Test%20mceliece8192128f/badge.svg?branch=master)
![Test ml-kem-1024](https://github.com/PQClean/PQClean/workflows/Test%20ml-kem-1024/badge.svg?branch=master)
![Test ml-kem-512](https://github.com/PQClean/PQClean/workflows/Test%20ml-kem-512/badge.svg?branch=master)
![Test ml-kem-768](https://github.com/PQClean/PQClean/workflows/Test%20ml-kem-768/badge.svg?branch=master)
![Test falcon-1024](https://github.com/PQClean/PQClean/workflows/Test%20falcon-1024/badge.svg?branch=master)
![Test falcon-512](https://github.com/PQClean/PQClean/workflows/Test%20falcon-512/badge.svg?branch=master)
![Test falcon-padded-1024](https://github.com/PQClean/PQClean/workflows/Test%20falcon-padded-1024/badge.svg?branch=master)
![Test falcon-padded-512](https://github.com/PQClean/PQClean/workflows/Test%20falcon-padded-512/badge.svg?branch=master)
![Test ml-dsa-44](https://github.com/PQClean/PQClean/workflows/Test%20ml-dsa-44/badge.svg?branch=master)
![Test ml-dsa-65](https://github.com/PQClean/PQClean/workflows/Test%20ml-dsa-65/badge.svg?branch=master)
![Test ml-dsa-87](https://github.com/PQClean/PQClean/workflows/Test%20ml-dsa-87/badge.svg?branch=master)
![Test sphincs-sha2-128f-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-sha2-128f-simple/badge.svg?branch=master)
![Test sphincs-sha2-128s-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-sha2-128s-simple/badge.svg?branch=master)
![Test sphincs-sha2-192f-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-sha2-192f-simple/badge.svg?branch=master)
![Test sphincs-sha2-192s-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-sha2-192s-simple/badge.svg?branch=master)
![Test sphincs-sha2-256f-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-sha2-256f-simple/badge.svg?branch=master)
![Test sphincs-sha2-256s-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-sha2-256s-simple/badge.svg?branch=master)
![Test sphincs-shake-128f-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-shake-128f-simple/badge.svg?branch=master)
![Test sphincs-shake-128s-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-shake-128s-simple/badge.svg?branch=master)
![Test sphincs-shake-192f-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-shake-192f-simple/badge.svg?branch=master)
![Test sphincs-shake-192s-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-shake-192s-simple/badge.svg?branch=master)
![Test sphincs-shake-256f-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-shake-256f-simple/badge.svg?branch=master)
![Test sphincs-shake-256s-simple](https://github.com/PQClean/PQClean/workflows/Test%20sphincs-shake-256s-simple/badge.svg?branch=master)

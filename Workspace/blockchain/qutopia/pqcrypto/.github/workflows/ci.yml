on:
  push:
  pull_request:

name: Continuous integration

jobs:
  ci:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        rust:
          - stable
          - beta
          - nightly
    env:
      CARGO_TERM_COLOR: always
      # 20 MiB stack
      RUST_MIN_STACK: 20971520

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      - name: Install Rust
        run: |
          rustup set auto-self-update disable
          rustup toolchain install ${{ matrix.rust }} --profile minimal --component rustfmt,clippy
          rustup default ${{ matrix.rust }}
          echo CARGO_TERM_COLOR=always >> $GITHUB_ENV
          echo CARGO_INCREMENTAL=0 >> $GITHUB_ENV
          echo RUST_BACKTRACE=1 >> $GITHUB_ENV

      - name: build with cryptographically-insecure turned on (if available)
        run: cargo build --features cryptographically-insecure

      - name: Run tests with cryptographically-insecure turned on
        run: cargo test --features cryptographically-insecure

      - name: Run cargo check with all features
        run: cargo check --all-features

      - name: Run cargo fmt
        run: cargo fmt --all -- --check

      - name: run Cargo clippy
        run: cargo clippy -- -D warnings -A deprecated

  android:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        rust:
          - stable
          - beta
          - nightly
    env:
      CARGO_TERM_COLOR: always
      # 20 MiB stack
      RUST_MIN_STACK: 20971520

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true

      - name: Install Rust
        run: |
          rustup set auto-self-update disable
          rustup toolchain install ${{ matrix.rust }} --profile minimal --component rustfmt,clippy
          rustup target add aarch64-linux-android
          echo CARGO_TERM_COLOR=always >> $GITHUB_ENV
          echo CARGO_INCREMENTAL=0 >> $GITHUB_ENV
          echo RUST_BACKTRACE=1 >> $GITHUB_ENV

      - name: Setup Android SDK
        uses: android-actions/setup-android@07976c6290703d34c16d382cb36445f98bb43b1f #3.2.0

      - name: Setup Android NDK
        run: sdkmanager "ndk;26.1.10909125"

      - name: Compile for Android
        run: |
          cargo install cargo-ndk
          cargo ndk -t aarch64-linux-android build

  # wasi:
  #   name: wasi
  #   runs-on: ubuntu-latest
  #   strategy:
  #     matrix:
  #       rust:
  #         - stable
  #         - beta
  #         - nightly
  #   env:
  #     # 20 MiB stack
  #     RUST_MIN_STACK: 20971520
  #   steps:
  #     - uses: actions/checkout@v4
  #       with:
  #         submodules: true

  #     - name: Install Rust
  #       run: |
  #         rustup set auto-self-update disable
  #         rustup toolchain install ${{ matrix.rust }} --profile minimal --component rustfmt clippy
  #         rustup default ${{ matrix.rust }}
  #         echo CARGO_TERM_COLOR=always >> $GITHUB_ENV
  #         echo CARGO_INCREMENTAL=0 >> $GITHUB_ENV
  #         echo RUST_BACKTRACE=1 >> $GITHUB_ENV

  #     - name: Install WASI
  #       run: cargo install cargo-wasi
  #     - name: Install wasmtime
  #       run: curl https://wasmtime.dev/install.sh -sSf | bash
  #     - name: Install WASI SDK
  #       run: |
  #         wget https://github.com/WebAssembly/wasi-sdk/releases/download/wasi-sdk-12/wasi-sysroot-12.0.tar.gz
  #         tar -xvzf wasi-sysroot-12.0.tar.gz
  #         rm wasi-sysroot-12.0.tar.gz
  #     - name: Run tests
  #       run: |
  #         export WASI_SDK_DIR="$(pwd)/wasi-sysroot"
  #         export WASMTIME_HOME="$(pwd)/.wasmtime"
  #         export PATH="$WASMTIME_HOME/bin:$PATH"
  #         cargo wasi build --features cryptographically-insecure
  #         #cargo wasi test -- --nocapture

#  vim: set ft=yaml ts=2 sw=2 tw=0 et :

<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="0 0 5547.81103515625 1121.0390625" style="max-width: 5547.81103515625px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4"><style>#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;fill:#ccc;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .error-icon{fill:#a44141;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .error-text{fill:#ddd;stroke:#ddd;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edge-thickness-normal{stroke-width:1px;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edge-thickness-thick{stroke-width:3.5px;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edge-pattern-solid{stroke-dasharray:0;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edge-thickness-invisible{stroke-width:0;fill:none;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edge-pattern-dashed{stroke-dasharray:3;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edge-pattern-dotted{stroke-dasharray:2;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .marker{fill:lightgrey;stroke:lightgrey;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .marker.cross{stroke:lightgrey;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 svg{font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:16px;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 p{margin:0;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .label{font-family:"trebuchet ms",verdana,arial,sans-serif;color:#ccc;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .cluster-label text{fill:#F9FFFE;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .cluster-label span{color:#F9FFFE;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .cluster-label span p{background-color:transparent;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .label text,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 span{fill:#ccc;color:#ccc;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node rect,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node circle,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node ellipse,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node polygon,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node path{fill:#1f2020;stroke:#ccc;stroke-width:1px;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .rough-node .label text,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node .label text,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .image-shape .label,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .icon-shape .label{text-anchor:middle;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .rough-node .label,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node .label,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .image-shape .label,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .icon-shape .label{text-align:center;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .node.clickable{cursor:pointer;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .root .anchor path{fill:lightgrey!important;stroke-width:0;stroke:lightgrey;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .arrowheadPath{fill:lightgrey;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edgePath .path{stroke:lightgrey;stroke-width:2.0px;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .flowchart-link{stroke:lightgrey;fill:none;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edgeLabel{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edgeLabel p{background-color:hsl(0, 0%, 34.4117647059%);}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .edgeLabel rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .labelBkg{background-color:rgba(87.75, 87.75, 87.75, 0.5);}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .cluster rect{fill:hsl(180, 1.5873015873%, 28.3529411765%);stroke:rgba(255, 255, 255, 0.25);stroke-width:1px;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .cluster text{fill:#F9FFFE;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .cluster span{color:#F9FFFE;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:"trebuchet ms",verdana,arial,sans-serif;font-size:12px;background:hsl(20, 1.5873015873%, 12.3529411765%);border:1px solid rgba(255, 255, 255, 0.25);border-radius:2px;pointer-events:none;z-index:100;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#ccc;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 rect.text{fill:none;stroke-width:0;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .icon-shape,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .image-shape{background-color:hsl(0, 0%, 34.4117647059%);text-align:center;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .icon-shape p,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .image-shape p{background-color:hsl(0, 0%, 34.4117647059%);padding:2px;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .icon-shape rect,#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .image-shape rect{opacity:0.5;background-color:hsl(0, 0%, 34.4117647059%);fill:hsl(0, 0%, 34.4117647059%);}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .application&gt;*{fill:#e3f2fd!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .application span{fill:#e3f2fd!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .rpc&gt;*{fill:#f3e5f5!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .rpc span{fill:#f3e5f5!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .engine&gt;*{fill:#e8f5e8!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .engine span{fill:#e8f5e8!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .execution&gt;*{fill:#fff3e0!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .execution span{fill:#fff3e0!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .consensus&gt;*{fill:#fce4ec!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .consensus span{fill:#fce4ec!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .network&gt;*{fill:#e0f2f1!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .network span{fill:#e0f2f1!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .sync&gt;*{fill:#f1f8e9!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .sync span{fill:#f1f8e9!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .storage&gt;*{fill:#fff8e1!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .storage span{fill:#fff8e1!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .foundation&gt;*{fill:#fafafa!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .foundation span{fill:#fafafa!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .optimism&gt;*{fill:#ffebee!important;}#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4 .optimism span{fill:#ffebee!important;}</style><g><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 10 5 L 0 10 z"></path></marker><marker orient="auto" markerHeight="8" markerWidth="8" markerUnits="userSpaceOnUse" refY="5" refX="4.5" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointStart"><path style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 5 L 10 10 L 10 0 z"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="11" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-circleEnd"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5" refX="-1" viewBox="0 0 10 10" class="marker flowchart-v2" id="mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-circleStart"><circle style="stroke-width: 1; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"></circle></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="12" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-crossEnd"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><marker orient="auto" markerHeight="11" markerWidth="11" markerUnits="userSpaceOnUse" refY="5.2" refX="-1" viewBox="0 0 11 11" class="marker cross flowchart-v2" id="mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-crossStart"><path style="stroke-width: 2; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 l 9,9 M 10,1 l -9,9"></path></marker><g class="root"><g class="clusters"><g data-look="classic" id="subGraph9" class="cluster"><rect height="104.00390243530273" width="1270.900058746338" y="8" x="847.7395629882812" style=""></rect><g transform="translate(1430.6765727996826, 8)" class="cluster-label"><foreignObject height="24.00390625" width="105.02603912353516"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Optimism 支持</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph8" class="cluster"><rect height="306.015625" width="801.1262817382812" y="316.00780487060547" x="8" style=""></rect><g transform="translate(314.18163299560547, 316.00780487060547)" class="cluster-label"><foreignObject height="24.00390625" width="188.7630157470703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>基础层 (Foundation Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph7" class="cluster"><rect height="441.01560974121094" width="744.2317504882812" y="672.0234298706055" x="1603.5842971801758" style=""></rect><g transform="translate(1894.3264770507812, 672.0234298706055)" class="cluster-label"><foreignObject height="24.00390625" width="162.7473907470703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>存储层 (Storage Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph6" class="cluster"><rect height="282.01171493530273" width="545.4492034912109" y="494.01561737060547" x="4133.164329528809" style=""></rect><g transform="translate(4335.394142150879, 494.01561737060547)" class="cluster-label"><foreignObject height="24.00390625" width="140.9895782470703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>同步层 (Sync Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph5" class="cluster"><rect height="306.015625" width="1000.8137817382812" y="316.00780487060547" x="2468.958625793457" style=""></rect><g transform="translate(2884.9644775390625, 316.00780487060547)" class="cluster-label"><foreignObject height="24.00390625" width="168.8020782470703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>网络层 (Network Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph4" class="cluster"><rect height="282.01171493530273" width="639.609375" y="162.00390243530273" x="829.1262817382812" style=""></rect><g transform="translate(1058.292953491211, 162.00390243530273)" class="cluster-label"><foreignObject height="24.00390625" width="181.27603149414062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>共识层 (Consensus Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph3" class="cluster"><rect height="282.01171493530273" width="841.1979064941406" y="494.01561737060547" x="4698.6135330200195" style=""></rect><g transform="translate(5029.73983001709, 494.01561737060547)" class="cluster-label"><foreignObject height="24.00390625" width="178.9453125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>执行层 (Execution Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph2" class="cluster"><rect height="306.015625" width="623.3919219970703" y="316.00780487060547" x="3489.7724075317383" style=""></rect><g transform="translate(3723.4117279052734, 316.00780487060547)" class="cluster-label"><foreignObject height="24.00390625" width="156.11328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>引擎层 (Engine Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph1" class="cluster"><rect height="306.015625" width="960.2229690551758" y="316.00780487060547" x="1488.7356567382812" style=""></rect><g transform="translate(1900.2631568908691, 316.00780487060547)" class="cluster-label"><foreignObject height="24.00390625" width="137.16796875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>RPC 层 (RPC Layer)</p></span></div></foreignObject></g></g><g data-look="classic" id="subGraph0" class="cluster"><rect height="258.00780487060547" width="1829.5247077941895" y="8" x="2138.639621734619" style=""></rect><g transform="translate(2958.480100631714, 8)" class="cluster-label"><foreignObject height="24.00390625" width="189.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>应用层 (Application Layer)</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CLI_NODE_0" d="M3700.856,87.004L3700.856,91.171C3700.856,95.337,3700.856,103.671,3700.856,112.004C3700.856,120.337,3700.856,128.671,3700.856,137.004C3700.856,145.337,3700.856,153.671,3597.726,165.306C3494.596,176.942,3288.335,191.881,3185.205,199.35L3082.074,206.819"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NODE_RPC_1" d="M2887.596,220.489L2776.14,228.075C2664.685,235.662,2441.773,250.835,2330.317,262.588C2218.861,274.341,2218.861,282.674,2218.861,291.008C2218.861,299.341,2218.861,307.674,2178.492,319.248C2138.122,330.821,2057.383,345.635,2017.014,353.042L1976.645,360.448"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NODE_ENGINE_TREE_2" d="M3078.085,219.137L3223.098,226.949C3368.111,234.76,3658.138,250.384,3803.151,262.363C3948.164,274.341,3948.164,282.674,3948.164,291.008C3948.164,299.341,3948.164,307.674,3948.164,317.341C3948.164,327.008,3948.164,338.009,3948.164,343.509L3948.164,349.01"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NODE_NETWORK_3" d="M2982.84,241.008L2982.84,245.174C2982.84,249.341,2982.84,257.674,2982.84,266.008C2982.84,274.341,2982.84,282.674,2982.84,291.008C2982.84,299.341,2982.84,307.674,2982.84,317.341C2982.84,327.008,2982.84,338.009,2982.84,343.509L2982.84,349.01"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RPC_RPC_API_4" d="M1767.326,406.601L1743.242,412.836C1719.158,419.072,1670.989,431.544,1646.905,441.946C1622.821,452.349,1622.821,460.682,1622.821,469.016C1622.821,477.349,1622.821,485.682,1622.821,495.349C1622.821,505.016,1622.821,516.017,1622.821,521.517L1622.821,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RPC_RPC_ETH_5" d="M1928.552,407.014L1941.921,413.181C1955.289,419.348,1982.026,431.682,1995.395,442.015C2008.763,452.349,2008.763,460.682,2008.763,469.016C2008.763,477.349,2008.763,485.682,2008.763,495.349C2008.763,505.016,2008.763,516.017,2008.763,521.517L2008.763,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_RPC_ETH_STORAGE_API_6" d="M2008.763,585.021L2008.763,591.188C2008.763,597.355,2008.763,609.689,2008.763,620.023C2008.763,630.357,2008.763,638.69,2008.763,647.023C2008.763,655.357,2008.763,663.69,2008.763,671.357C2008.763,679.023,2008.763,686.023,2008.763,689.523L2008.763,693.023"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ENGINE_TREE_ENGINE_SERVICE_7" d="M3844.694,401.963L3811.657,408.971C3778.619,415.98,3712.544,429.998,3679.506,441.173C3646.468,452.349,3646.468,460.682,3646.468,469.016C3646.468,477.349,3646.468,485.682,3646.468,495.349C3646.468,505.016,3646.468,516.017,3646.468,521.517L3646.468,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ENGINE_TREE_ENGINE_PRIMITIVES_8" d="M3948.164,407.014L3948.164,413.181C3948.164,419.348,3948.164,431.682,3948.164,442.015C3948.164,452.349,3948.164,460.682,3948.164,469.016C3948.164,477.349,3948.164,485.682,3948.164,493.349C3948.164,501.016,3948.164,508.016,3948.164,511.516L3948.164,515.016"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ENGINE_TREE_EVM_9" d="M3982.969,407.014L3990.919,413.181C3998.868,419.348,4014.766,431.682,4022.715,442.015C4030.664,452.349,4030.664,460.682,4030.664,469.016C4030.664,477.349,4030.664,485.682,4189.064,499.448C4347.463,513.214,4664.261,532.413,4822.66,542.012L4981.06,551.611"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EVM_REVM_10" d="M4985.052,582.583L4957.824,589.157C4930.595,595.73,4876.137,608.877,4848.909,619.617C4821.68,630.357,4821.68,638.69,4821.68,647.023C4821.68,655.357,4821.68,663.69,4821.68,671.357C4821.68,679.023,4821.68,686.023,4821.68,689.523L4821.68,693.023"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EVM_EXECUTION_TYPES_11" d="M5086.804,585.021L5086.804,591.188C5086.804,597.355,5086.804,609.689,5086.804,620.023C5086.804,630.357,5086.804,638.69,5086.804,647.023C5086.804,655.357,5086.804,663.69,5086.804,671.357C5086.804,679.023,5086.804,686.023,5086.804,689.523L5086.804,693.023"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_EVM_TXPOOL_12" d="M5188.555,579.908L5221.185,586.927C5253.815,593.946,5319.076,607.985,5351.706,619.171C5384.336,630.357,5384.336,638.69,5384.336,647.023C5384.336,655.357,5384.336,663.69,5384.336,671.357C5384.336,679.023,5384.336,686.023,5384.336,689.523L5384.336,693.023"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONSENSUS_CONSENSUS_COMMON_13" d="M989.319,241.008L990.12,245.174C990.921,249.341,992.524,257.674,993.325,266.008C994.126,274.341,994.126,282.674,994.126,291.008C994.126,299.341,994.126,307.674,994.126,315.341C994.126,323.008,994.126,330.008,994.126,333.508L994.126,337.008"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CONSENSUS_PRIMITIVES_14" d="M946.481,241.008L940.672,245.174C934.863,249.341,923.244,257.674,917.435,266.008C911.626,274.341,911.626,282.674,911.626,291.008C911.626,299.341,911.626,307.674,849.231,320.095C786.835,332.516,662.044,349.023,599.649,357.277L537.254,365.531"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NETWORK_DISCV4_15" d="M2884.403,396.637L2837.649,404.534C2790.895,412.43,2697.388,428.223,2650.634,440.286C2603.881,452.349,2603.881,460.682,2603.881,469.016C2603.881,477.349,2603.881,485.682,2603.881,495.349C2603.881,505.016,2603.881,516.017,2603.881,521.517L2603.881,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NETWORK_DISCV5_16" d="M2928.369,407.014L2915.928,413.181C2903.487,419.348,2878.606,431.682,2866.165,442.015C2853.724,452.349,2853.724,460.682,2853.724,469.016C2853.724,477.349,2853.724,485.682,2853.724,495.349C2853.724,505.016,2853.724,516.017,2853.724,521.517L2853.724,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NETWORK_ETH_WIRE_17" d="M3037.312,407.014L3049.753,413.181C3062.194,419.348,3087.075,431.682,3099.516,442.015C3111.957,452.349,3111.957,460.682,3111.957,469.016C3111.957,477.349,3111.957,485.682,3111.957,495.349C3111.957,505.016,3111.957,516.017,3111.957,521.517L3111.957,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_NETWORK_P2P_18" d="M3081.278,397.055L3126.485,404.881C3171.692,412.708,3262.106,428.362,3307.313,440.355C3352.52,452.349,3352.52,460.682,3352.52,469.016C3352.52,477.349,3352.52,485.682,3352.52,495.349C3352.52,505.016,3352.52,516.017,3352.52,521.517L3352.52,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_STAGES_STAGES_API_19" d="M4275.167,585.021L4274.978,591.188C4274.789,597.355,4274.411,609.689,4274.222,620.023C4274.033,630.357,4274.033,638.69,4274.033,647.023C4274.033,655.357,4274.033,663.69,4274.033,671.357C4274.033,679.023,4274.033,686.023,4274.033,689.523L4274.033,693.023"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_STAGES_DOWNLOADERS_20" d="M4375.985,582.562L4402.78,589.139C4429.576,595.716,4483.167,608.87,4509.963,619.613C4536.758,630.357,4536.758,638.69,4536.758,647.023C4536.758,655.357,4536.758,663.69,4536.758,671.357C4536.758,679.023,4536.758,686.023,4536.758,689.523L4536.758,693.023"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_STAGES_STORAGE_API_21" d="M4245.452,585.021L4238.477,591.188C4231.501,597.355,4217.55,609.689,4210.574,620.023C4203.599,630.357,4203.599,638.69,4203.599,647.023C4203.599,655.357,4203.599,663.69,3856.798,676.073C3509.998,688.457,2816.396,704.89,2469.595,713.107L2122.795,721.324"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_STORAGE_API_PROVIDER_22" d="M1898.731,745.829L1873.331,750.862C1847.931,755.895,1797.131,765.961,1771.732,775.161C1746.332,784.361,1746.332,792.694,1746.332,800.361C1746.332,808.027,1746.332,815.027,1746.332,818.527L1746.332,822.027"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PROVIDER_DB_23" d="M1746.332,880.031L1746.332,884.198C1746.332,888.365,1746.332,896.698,1746.332,904.365C1746.332,912.031,1746.332,919.031,1746.332,922.531L1746.332,926.031"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_DB_DB_API_24" d="M1746.332,984.035L1746.332,988.202C1746.332,992.368,1746.332,1000.702,1746.332,1008.368C1746.332,1016.035,1746.332,1023.035,1746.332,1026.535L1746.332,1030.035"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_STORAGE_API_STATIC_FILE_25" d="M2008.763,751.027L2008.763,755.194C2008.763,759.361,2008.763,767.694,2008.763,776.027C2008.763,784.361,2008.763,792.694,2008.763,800.361C2008.763,808.027,2008.763,815.027,2008.763,818.527L2008.763,822.027"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_STORAGE_API_TRIE_26" d="M2118.796,748.972L2138.685,753.481C2158.575,757.99,2198.353,767.009,2218.242,775.685C2238.132,784.361,2238.132,792.694,2238.132,800.361C2238.132,808.027,2238.132,815.027,2238.132,818.527L2238.132,822.027"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PRIMITIVES_PRIMITIVES_TRAITS_27" d="M322.285,406.268L297.007,412.56C271.728,418.851,221.171,431.433,195.893,441.891C170.614,452.349,170.614,460.682,170.614,469.016C170.614,477.349,170.614,485.682,170.614,495.349C170.614,505.016,170.614,516.017,170.614,521.517L170.614,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PRIMITIVES_CHAINSPEC_28" d="M435.131,407.014L436.808,413.181C438.485,419.348,441.84,431.682,443.517,442.015C445.194,452.349,445.194,460.682,445.194,469.016C445.194,477.349,445.194,485.682,445.194,495.349C445.194,505.016,445.194,516.017,445.194,521.517L445.194,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_PRIMITIVES_ERRORS_29" d="M533.288,406.455L558.264,412.715C583.24,418.975,633.191,431.495,658.167,441.922C683.143,452.349,683.143,460.682,683.143,469.016C683.143,477.349,683.143,485.682,683.143,495.349C683.143,505.016,683.143,516.017,683.143,521.517L683.143,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OP_NODE_NODE_30" d="M1788.48,87.004L1788.48,91.171C1788.48,95.337,1788.48,103.671,1859.36,112.004C1930.241,120.337,2072.001,128.671,2142.881,137.004C2213.762,145.337,2213.762,153.671,2325.402,165.386C2437.043,177.101,2660.324,192.199,2771.965,199.747L2883.605,207.296"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OP_EVM_EVM_31" d="M2002.774,87.004L2002.774,91.171C2002.774,95.337,2002.774,103.671,2516.779,112.004C3030.784,120.337,4058.794,128.671,4572.799,137.004C5086.804,145.337,5086.804,153.671,5086.804,166.504C5086.804,179.338,5086.804,196.672,5086.804,214.006C5086.804,231.34,5086.804,248.674,5086.804,261.507C5086.804,274.341,5086.804,282.674,5086.804,291.008C5086.804,299.341,5086.804,307.674,5086.804,322.508C5086.804,337.342,5086.804,358.677,5086.804,380.012C5086.804,401.346,5086.804,422.681,5086.804,437.515C5086.804,452.349,5086.804,460.682,5086.804,469.016C5086.804,477.349,5086.804,485.682,5086.804,495.349C5086.804,505.016,5086.804,516.017,5086.804,521.517L5086.804,527.018"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OP_RPC_RPC_32" d="M1577.582,87.004L1577.582,91.171C1577.582,95.337,1577.582,103.671,1577.582,112.004C1577.582,120.337,1577.582,128.671,1577.582,137.004C1577.582,145.337,1577.582,153.671,1577.582,166.504C1577.582,179.338,1577.582,196.672,1577.582,214.006C1577.582,231.34,1577.582,248.674,1577.582,261.507C1577.582,274.341,1577.582,282.674,1577.582,291.008C1577.582,299.341,1577.582,307.674,1608.555,318.62C1639.527,329.565,1701.473,343.123,1732.446,349.902L1763.419,356.681"></path><path marker-end="url(#mermaid-1de9ad43-7724-42d8-9755-b67b7c32b5c4_flowchart-v2-pointEnd)" style="" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_OP_CONSENSUS_CONSENSUS_33" d="M984.126,87.004L984.126,91.171C984.126,95.337,984.126,103.671,984.126,112.004C984.126,120.337,984.126,128.671,984.126,137.004C984.126,145.337,984.126,153.671,984.126,161.337C984.126,169.004,984.126,176.004,984.126,179.504L984.126,183.004"></path></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g><g class="nodes"><g transform="translate(3700.8563957214355, 60.00195121765137)" id="flowchart-CLI-245" class="node default application"><rect height="54.00390625" width="172.72135162353516" y="-27.001953125" x="-86.36067581176758" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-56.36067581176758, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="112.72135162353516"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>cli - 命令行接口</p></span></div></foreignObject></g></g><g transform="translate(2982.8404579162598, 214.0058536529541)" id="flowchart-NODE-246" class="node default application"><rect height="54.00390625" width="190.48828125" y="-27.001953125" x="-95.244140625" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-65.244140625, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="130.48828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>node - 节点构建器</p></span></div></foreignObject></g></g><g transform="translate(3690.693630218506, 214.0058536529541)" id="flowchart-METRICS-247" class="node default application"><rect height="54.00390625" width="193.046875" y="-27.001953125" x="-96.5234375" style="fill:#e3f2fd !important" class="basic label-container"></rect><g transform="translate(-66.5234375, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="133.046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>metrics - 指标监控</p></span></div></foreignObject></g></g><g transform="translate(1870.0182037353516, 380.01171112060547)" id="flowchart-RPC-248" class="node default rpc"><rect height="54.00390625" width="205.3841094970703" y="-27.001953125" x="-102.69205474853516" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-72.69205474853516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="145.3841094970703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>rpc - JSON-RPC 服务</p></span></div></foreignObject></g></g><g transform="translate(1622.8209419250488, 558.0195236206055)" id="flowchart-RPC_API-249" class="node default rpc"><rect height="54.00390625" width="190.95703125" y="-27.001953125" x="-95.478515625" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-65.478515625, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="130.95703125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>rpc-api - RPC 接口</p></span></div></foreignObject></g></g><g transform="translate(2008.7633209228516, 558.0195236206055)" id="flowchart-RPC_ETH-250" class="node default rpc"><rect height="54.00390625" width="231.6536407470703" y="-27.001953125" x="-115.82682037353516" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-85.82682037353516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="171.6536407470703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>rpc-eth-api - 以太坊 API</p></span></div></foreignObject></g></g><g transform="translate(2294.274383544922, 558.0195236206055)" id="flowchart-ENGINE_API-251" class="node default rpc"><rect height="54.00390625" width="239.3684844970703" y="-27.001953125" x="-119.68424224853516" style="fill:#f3e5f5 !important" class="basic label-container"></rect><g transform="translate(-89.68424224853516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="179.3684844970703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>rpc-engine-api - 引擎 API</p></span></div></foreignObject></g></g><g transform="translate(3948.1643295288086, 380.01171112060547)" id="flowchart-ENGINE_TREE-252" class="node default engine"><rect height="54.00390625" width="206.94009399414062" y="-27.001953125" x="-103.47004699707031" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-73.47004699707031, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="146.94009399414062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>engine-tree - 引擎树</p></span></div></foreignObject></g></g><g transform="translate(3646.4683685302734, 558.0195236206055)" id="flowchart-ENGINE_SERVICE-253" class="node default engine"><rect height="54.00390625" width="243.3919219970703" y="-27.001953125" x="-121.69596099853516" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-91.69596099853516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="183.3919219970703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>engine-service - 引擎服务</p></span></div></foreignObject></g></g><g transform="translate(3948.1643295288086, 558.0195236206055)" id="flowchart-ENGINE_PRIMITIVES-254" class="node default engine"><rect height="78.0078125" width="260" y="-39.00390625" x="-130" style="fill:#e8f5e8 !important" class="basic label-container"></rect><g transform="translate(-100, -24.00390625)" style="" class="label"><rect></rect><foreignObject height="48.0078125" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>engine-primitives - 引擎基础类型</p></span></div></foreignObject></g></g><g transform="translate(5086.803634643555, 558.0195236206055)" id="flowchart-EVM-255" class="node default execution"><rect height="54.00390625" width="203.50259399414062" y="-27.001953125" x="-101.75129699707031" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-71.75129699707031, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="143.50259399414062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>evm - EVM 执行引擎</p></span></div></foreignObject></g></g><g transform="translate(4821.6799392700195, 724.0253810882568)" id="flowchart-REVM-256" class="node default execution"><rect height="54.00390625" width="176.1328125" y="-27.001953125" x="-88.06640625" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-58.06640625, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="116.1328125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>revm - Rust EVM</p></span></div></foreignObject></g></g><g transform="translate(5086.803634643555, 724.0253810882568)" id="flowchart-EXECUTION_TYPES-257" class="node default execution"><rect height="54.00390625" width="254.1145782470703" y="-27.001953125" x="-127.05728912353516" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-97.05728912353516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="194.1145782470703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>execution-types - 执行类型</p></span></div></foreignObject></g></g><g transform="translate(5384.336181640625, 724.0253810882568)" id="flowchart-TXPOOL-258" class="node default execution"><rect height="54.00390625" width="240.9505157470703" y="-27.001953125" x="-120.47525787353516" style="fill:#fff3e0 !important" class="basic label-container"></rect><g transform="translate(-90.47525787353516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="180.9505157470703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>transaction-pool - 交易池</p></span></div></foreignObject></g></g><g transform="translate(984.1262817382812, 214.0058536529541)" id="flowchart-CONSENSUS-259" class="node default consensus"><rect height="54.00390625" width="210.41015625" y="-27.001953125" x="-105.205078125" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-75.205078125, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="150.41015625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>consensus - 共识验证</p></span></div></foreignObject></g></g><g transform="translate(994.1262817382812, 380.01171112060547)" id="flowchart-CONSENSUS_COMMON-260" class="node default consensus"><rect height="78.0078125" width="260" y="-39.00390625" x="-130" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-100, -24.00390625)" style="" class="label"><rect></rect><foreignObject height="48.0078125" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>consensus-common - 共识通用</p></span></div></foreignObject></g></g><g transform="translate(1303.9309692382812, 380.01171112060547)" id="flowchart-CONSENSUS_DEBUG-261" class="node default consensus"><rect height="54.00390625" width="259.609375" y="-27.001953125" x="-129.8046875" style="fill:#fce4ec !important" class="basic label-container"></rect><g transform="translate(-99.8046875, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="199.609375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>consensus-debug - 共识调试</p></span></div></foreignObject></g></g><g transform="translate(2982.8404579162598, 380.01171112060547)" id="flowchart-NETWORK-262" class="node default network"><rect height="54.00390625" width="196.875" y="-27.001953125" x="-98.4375" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-68.4375, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="136.875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>network - P2P 网络</p></span></div></foreignObject></g></g><g transform="translate(2603.880500793457, 558.0195236206055)" id="flowchart-DISCV4-263" class="node default network"><rect height="54.00390625" width="199.84375" y="-27.001953125" x="-99.921875" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-69.921875, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="139.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>discv4 - 节点发现v4</p></span></div></foreignObject></g></g><g transform="translate(2853.724250793457, 558.0195236206055)" id="flowchart-DISCV5-264" class="node default network"><rect height="54.00390625" width="199.84375" y="-27.001953125" x="-99.921875" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-69.921875, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="139.84375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>discv5 - 节点发现v5</p></span></div></foreignObject></g></g><g transform="translate(3111.9566650390625, 558.0195236206055)" id="flowchart-ETH_WIRE-265" class="node default network"><rect height="54.00390625" width="216.62109375" y="-27.001953125" x="-108.310546875" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-78.310546875, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="156.62109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>eth-wire - 以太坊协议</p></span></div></foreignObject></g></g><g transform="translate(3352.519805908203, 558.0195236206055)" id="flowchart-P2P-266" class="node default network"><rect height="54.00390625" width="164.5052032470703" y="-27.001953125" x="-82.25260162353516" style="fill:#e0f2f1 !important" class="basic label-container"></rect><g transform="translate(-52.252601623535156, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="104.50520324707031"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>p2p - P2P 核心</p></span></div></foreignObject></g></g><g transform="translate(4275.994735717773, 558.0195236206055)" id="flowchart-STAGES-267" class="node default sync"><rect height="54.00390625" width="199.98046875" y="-27.001953125" x="-99.990234375" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-69.990234375, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="139.98046875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>stages - 分阶段同步</p></span></div></foreignObject></g></g><g transform="translate(4274.033470153809, 724.0253810882568)" id="flowchart-STAGES_API-268" class="node default sync"><rect height="54.00390625" width="211.73828125" y="-27.001953125" x="-105.869140625" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-75.869140625, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="151.73828125"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>stages-api - 同步接口</p></span></div></foreignObject></g></g><g transform="translate(4536.758071899414, 724.0253810882568)" id="flowchart-DOWNLOADERS-269" class="node default sync"><rect height="54.00390625" width="213.7109375" y="-27.001953125" x="-106.85546875" style="fill:#f1f8e9 !important" class="basic label-container"></rect><g transform="translate(-76.85546875, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="153.7109375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>downloaders - 下载器</p></span></div></foreignObject></g></g><g transform="translate(2008.7633209228516, 724.0253810882568)" id="flowchart-STORAGE_API-270" class="node default storage"><rect height="54.00390625" width="220.06509399414062" y="-27.001953125" x="-110.03254699707031" style="fill:#fff8e1 !important" class="basic label-container"></rect><g transform="translate(-80.03254699707031, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="160.06509399414062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>storage-api - 存储接口</p></span></div></foreignObject></g></g><g transform="translate(1746.331687927246, 853.0292835235596)" id="flowchart-PROVIDER-271" class="node default storage"><rect height="54.00390625" width="215.49478149414062" y="-27.001953125" x="-107.74739074707031" style="fill:#fff8e1 !important" class="basic label-container"></rect><g transform="translate(-77.74739074707031, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="155.49478149414062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>provider - 存储提供者</p></span></div></foreignObject></g></g><g transform="translate(1746.331687927246, 957.0331859588623)" id="flowchart-DB-272" class="node default storage"><rect height="54.00390625" width="173.34635162353516" y="-27.001953125" x="-86.67317581176758" style="fill:#fff8e1 !important" class="basic label-container"></rect><g transform="translate(-56.67317581176758, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="113.34635162353516"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>db - 数据库核心</p></span></div></foreignObject></g></g><g transform="translate(1746.331687927246, 1061.037088394165)" id="flowchart-DB_API-273" class="node default storage"><rect height="54.00390625" width="201.1067657470703" y="-27.001953125" x="-100.55338287353516" style="fill:#fff8e1 !important" class="basic label-container"></rect><g transform="translate(-70.55338287353516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="141.1067657470703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>db-api - 数据库接口</p></span></div></foreignObject></g></g><g transform="translate(2008.7633209228516, 853.0292835235596)" id="flowchart-STATIC_FILE-274" class="node default storage"><rect height="54.00390625" width="209.3684844970703" y="-27.001953125" x="-104.68424224853516" style="fill:#fff8e1 !important" class="basic label-container"></rect><g transform="translate(-74.68424224853516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="149.3684844970703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>static-file - 静态文件</p></span></div></foreignObject></g></g><g transform="translate(2238.131805419922, 853.0292835235596)" id="flowchart-TRIE-275" class="node default storage"><rect height="54.00390625" width="149.3684844970703" y="-27.001953125" x="-74.68424224853516" style="fill:#fff8e1 !important" class="basic label-container"></rect><g transform="translate(-44.684242248535156, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="89.36848449707031"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>trie - 状态树</p></span></div></foreignObject></g></g><g transform="translate(427.78676986694336, 380.01171112060547)" id="flowchart-PRIMITIVES-276" class="node default foundation"><rect height="54.00390625" width="211.00259399414062" y="-27.001953125" x="-105.50129699707031" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-75.50129699707031, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="151.00259399414062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>primitives - 基础类型</p></span></div></foreignObject></g></g><g transform="translate(170.61392974853516, 558.0195236206055)" id="flowchart-PRIMITIVES_TRAITS-277" class="node default foundation"><rect height="54.00390625" width="255.2278594970703" y="-27.001953125" x="-127.61392974853516" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-97.61392974853516, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="195.2278594970703"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>primitives-traits - 基础特征</p></span></div></foreignObject></g></g><g transform="translate(445.1940002441406, 558.0195236206055)" id="flowchart-CHAINSPEC-278" class="node default foundation"><rect height="54.00390625" width="193.93228149414062" y="-27.001953125" x="-96.96614074707031" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-66.96614074707031, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="133.93228149414062"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>chainspec - 链规范</p></span></div></foreignObject></g></g><g transform="translate(683.1432113647461, 558.0195236206055)" id="flowchart-ERRORS-279" class="node default foundation"><rect height="54.00390625" width="181.9661407470703" y="-27.001953125" x="-90.98307037353516" style="fill:#fafafa !important" class="basic label-container"></rect><g transform="translate(-60.983070373535156, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="121.96614074707031"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>errors - 错误处理</p></span></div></foreignObject></g></g><g transform="translate(1788.4801216125488, 60.00195121765137)" id="flowchart-OP_NODE-280" class="node default optimism"><rect height="54.00390625" width="166.85546875" y="-27.001953125" x="-83.427734375" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-53.427734375, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="106.85546875"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>optimism-node</p></span></div></foreignObject></g></g><g transform="translate(2002.773738861084, 60.00195121765137)" id="flowchart-OP_EVM-281" class="node default optimism"><rect height="54.00390625" width="161.7317657470703" y="-27.001953125" x="-80.86588287353516" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-50.865882873535156, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="101.73176574707031"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>optimism-evm</p></span></div></foreignObject></g></g><g transform="translate(1577.5816841125488, 60.00195121765137)" id="flowchart-OP_RPC-282" class="node default optimism"><rect height="54.00390625" width="154.94140625" y="-27.001953125" x="-77.470703125" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-47.470703125, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="94.94140625"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>optimism-rpc</p></span></div></foreignObject></g></g><g transform="translate(984.1262817382812, 60.00195121765137)" id="flowchart-OP_CONSENSUS-283" class="node default optimism"><rect height="54.00390625" width="202.7734375" y="-27.001953125" x="-101.38671875" style="fill:#ffebee !important" class="basic label-container"></rect><g transform="translate(-71.38671875, -12.001953125)" style="" class="label"><rect></rect><foreignObject height="24.00390625" width="142.7734375"><div style="display: table-cell; white-space: nowrap; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>optimism-consensus</p></span></div></foreignObject></g></g></g></g></g></svg>